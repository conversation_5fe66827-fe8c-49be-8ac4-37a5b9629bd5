/*----------------------------------------------*/
/* app.css
/*----------------------------------------------*/
body, html {
	font-family: arial, sans-serif;
	font-size: 11pt;
	width: 100%;
	height: 100%;
	margin: 0;
	padding: 0;
	overflow: hidden;
}

span.header-light {
	color: gray;
}

#header {
	width: 100%;
	height: 40px;
    /* TODO
    overflow: hidden;
    */
	background: #4D4D4D url("header_background.png"/*tpa=http://www.json.org.cn/tools/JSONEditorOnline2.0/misc/img/header_background.png*/);
	color: white;
}

#logo {
	height: 32px;
	margin: 4px 10px;
	border: none;
}

#menu {
	position: absolute;
	top: 5px;
	right: 15px;
	font-size: 11pt;
}

#menu ul {
	list-style: none;
	margin: 0;
	padding: 0;
	clear: both;
}

#menu ul li {
	color: #e6e6e6;
	background: none;
	border: none;
	border-right: 1px solid #737373;
	height: 30px;
	padding: 0;
	margin: 0;
	float: left;
	position: relative;
	text-decoration: none;
}

#menu ul li:first-child {
	border-left: 1px solid #737373;
}

#menu ul li:hover {
	color: white;
	background-color: #737373;
}

#menu ul li ul {
	display: none;
}

#menu ul li:hover > ul {
	display: block;
}

#menu ul li ul {
	position: absolute;
	top: 30px;
	left: 0;
	z-index: 999;
	background: #f5f5f5;
	border: 1px solid lightgray;
	box-shadow: 0 0 15px rgba(128, 128, 128, 0.5);
}

#menu ul li ul li {
	color: #737373;
	background: none;
	border: none;
	margin: 0;
	padding: 0;
}

#menu ul li ul li:first-child {
	border-left: none;
}

#menu ul li ul li:hover {
	background-color: white;
	color: #737373;
}

#menu a {
	padding: 6px 10px;
	display: block;
	cursor: pointer;
}

#menu ul li ul li a {
	width: 80px;
}

#openMenuButton {
	font-size: 75%;
	margin-left: 2px;
}

#menu #open {
	cursor: default;
}

/* TODO: enable the menu with keys (when openMenuButton is active) */

#auto {
	width: 100%;
	height: 100%;
	margin: -40px 0 -24px 0;
	padding: 40px 0  24px 0;
	-moz-box-sizing: border-box;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	overflow: hidden;
}

#contents {
	width: 100%;
	height: 100%;
	overflow: hidden;
}

#jsonformatter, #jsoneditor {
	float: left;
	height: 100%;
	width: 400px;
	padding: 15px;
	-moz-box-sizing: border-box;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
}

#splitter {
	float: left;
	height: 100%;
	cursor: col-resize;
}

#footer {
	width: 100%;
	height: 23px;
	font-size: 10pt;
	overflow: hidden;
	color: #BFBFBF;
	border-top: 1px solid lightgray;
	text-align: center;
	background-color: #F5F5F5;
}

#footer-inner {
	margin: 4px;
}

a.header {
	color: white;
	text-decoration: none;
}

a.footer, a.adInfo {
	color: #BFBFBF;
	text-decoration: none;
}

a.footer:hover, a.adInfo:hover {
	color: red;
	text-decoration: underline;
}

#ad {
	float: left;
	padding: 40px 0 15px 0;
	position: relative;
}

#adInfo {
	text-align: center;
	color: #BFBFBF;
	font-size: 10pt;
	background-color: #f5f5f5;
	line-height: 150%;
	position: absolute;
	left: 0;
	top: 15px;
	width: 160px;
	padding-bottom: 5px;
}

#chromeAppInfo {
	line-height: normal;
	padding: 0 5px 20px 5px;
}

div.adSpace {
	height: 15px;
}

a.adInfo {
	text-decoration: underline;
}

div.error, div.notification {
	border-radius: 2px;
	padding: 5px;
	margin: 5px;
	box-shadow: 0 0 15px rgba(128, 128, 128, 0.5);
    /* TODO: add some transition effect */;
}

div.error {
	color: red;
	background-color: #FFC0CB;
	border: 1px solid red;
}

div.notification {
	color: #1a1a1a;
	background-color: #FFFFAB;
	border: 1px solid #e6d600;
}

pre.error {
	margin: 0 0 10px 0;
	white-space: pre-wrap;
	font-family: droid sans mono, monospace, courier new, courier, sans-serif;
	font-size: 10pt;
}

a.error {
	color: red;
	font-size: 8pt;
}

button.convert {
	cursor: default;
	padding: 2px;
}

div.convert-right, div.convert-left {
	width: 24px;
	height: 24px;
	margin: 0;
}

div.convert-right {
	background: #FFFFFF url("jsoneditor-icons.png" /*tpa=http://www.json.org.cn/tools/JSONEditorOnline2.0/misc/jsoneditor-icons.png*/) -168px 0;
}

div.convert-left {
	background: #FFFFFF url("jsoneditor-icons.png" /*tpa=http://www.json.org.cn/tools/JSONEditorOnline2.0/misc/jsoneditor-icons.png*/) -192px 0;
}

/*----------------------------------------------*/
/* contextmenu.css
/*----------------------------------------------*/

/* ContextMenu - main menu */

.jsoneditor-contextmenu {
	position: absolute;
}

.jsoneditor-contextmenu ul {
	position: relative;
	left: 0;
	top: 0;
	width: 124px;
	background: white;
	border: 1px solid #d3d3d3;
	box-shadow: 2px 2px 12px rgba(128, 128, 128, 0.3);
	z-index: 1;
	list-style: none;
	margin: 0;
	padding: 0;
}

.jsoneditor-contextmenu ul li button {
	padding: 0;
	margin: 0;
	width: 124px;
	height: 24px;
	border: none;
	cursor: pointer;
	color: #4d4d4d;
	background: transparent;
	line-height: 24px;
	text-align: left;
}

/* Fix button padding in firefox */
.jsoneditor-contextmenu ul li button::-moz-focus-inner {
	padding: 0;
	border: 0;
}

.jsoneditor-contextmenu ul li button:hover,
.jsoneditor-contextmenu ul li button:focus {
	color: #1a1a1a;
	background-color: #f5f5f5;
	outline: none;
}

.jsoneditor-contextmenu ul li button.default {
	width: 92px;
}

.jsoneditor-contextmenu ul li button.expand {
	float: right;
	width: 32px;
	height: 24px;
	border-left: 1px solid #e5e5e5;
}

.jsoneditor-contextmenu div.icon {
	float: left;
	width: 24px;
	height: 24px;
	border: none;
	padding: 0;
	margin: 0;
	background-image: url("jsoneditor-icons-1.png" /*tpa=http://www.json.org.cn/tools/JSONEditorOnline2.0/misc/img/jsoneditor-icons.png*/);
}

.jsoneditor-contextmenu ul li button div.expand {
	float: right;
	width: 24px;
	height: 24px;
	padding: 0;
	margin: 0 4px 0 0;
	background: url("jsoneditor-icons-1.png" /*tpa=http://www.json.org.cn/tools/JSONEditorOnline2.0/misc/img/jsoneditor-icons.png*/) 0 -72px;
	opacity: 0.4;
}

.jsoneditor-contextmenu ul li button:hover div.expand,
.jsoneditor-contextmenu ul li button:focus div.expand,
.jsoneditor-contextmenu ul li.selected div.expand,
.jsoneditor-contextmenu ul li button.expand:hover div.expand,
.jsoneditor-contextmenu ul li button.expand:focus div.expand {
	opacity: 1;
}

.jsoneditor-contextmenu .separator {
	height: 0;
	border-top: 1px solid #e5e5e5;
	padding-top: 5px;
	margin-top: 5px;
}

.jsoneditor-contextmenu button.remove > .icon {
	background-position: -24px -24px;
}

.jsoneditor-contextmenu button.remove:hover > .icon,
.jsoneditor-contextmenu button.remove:focus > .icon {
	background-position: -24px 0;
}

.jsoneditor-contextmenu button.append > .icon {
	background-position: 0 -24px;
}

.jsoneditor-contextmenu button.append:hover > .icon,
.jsoneditor-contextmenu button.append:focus > .icon {
	background-position: 0 0;
}

.jsoneditor-contextmenu button.insert > .icon {
	background-position: 0 -24px;
}

.jsoneditor-contextmenu button.insert:hover > .icon,
.jsoneditor-contextmenu button.insert:focus > .icon {
	background-position: 0 0;
}

.jsoneditor-contextmenu button.duplicate > .icon {
	background-position: -48px -24px;
}

.jsoneditor-contextmenu button.duplicate:hover > .icon,
.jsoneditor-contextmenu button.duplicate:focus > .icon {
	background-position: -48px 0;
}

.jsoneditor-contextmenu button.sort-asc > .icon {
	background-position: -168px -24px;
}

.jsoneditor-contextmenu button.sort-asc:hover > .icon,
.jsoneditor-contextmenu button.sort-asc:focus > .icon {
	background-position: -168px 0;
}

.jsoneditor-contextmenu button.sort-desc > .icon {
	background-position: -192px -24px;
}

.jsoneditor-contextmenu button.sort-desc:hover > .icon,
.jsoneditor-contextmenu button.sort-desc:focus > .icon {
	background-position: -192px 0;
}

/* ContextMenu - sub menu */

.jsoneditor-contextmenu ul li ul li .selected {
	background-color: #D5DDF6;
}

.jsoneditor-contextmenu ul li {
	overflow: hidden;
}

.jsoneditor-contextmenu ul li ul {
	display: none;
	position: relative;
	left: -10px;
	top: 0;
	border: none;
	box-shadow: inset 0 0 10px rgba(128, 128, 128, 0.5);
	padding: 0 10px;
    /* TODO: transition is not supported on IE8-9 */
	-webkit-transition: all 0.3s ease-out;
	-moz-transition: all 0.3s ease-out;
	-o-transition: all 0.3s ease-out;
	transition: all 0.3s ease-out;
}

.jsoneditor-contextmenu ul li.selected ul {;
}

.jsoneditor-contextmenu ul li ul li button {
	padding-left: 24px;
}

.jsoneditor-contextmenu ul li ul li button:hover,
.jsoneditor-contextmenu ul li ul li button:focus {
	background-color: #f5f5f5;
}

.jsoneditor-contextmenu button.type-string > .icon {
	background-position: -144px -24px;
}

.jsoneditor-contextmenu button.type-string:hover > .icon,
.jsoneditor-contextmenu button.type-string:focus > .icon,
.jsoneditor-contextmenu button.type-string.selected > .icon {
	background-position: -144px 0;
}

.jsoneditor-contextmenu button.type-auto > .icon {
	background-position: -120px -24px;
}

.jsoneditor-contextmenu button.type-auto:hover > .icon,
.jsoneditor-contextmenu button.type-auto:focus > .icon,
.jsoneditor-contextmenu button.type-auto.selected > .icon {
	background-position: -120px 0;
}

.jsoneditor-contextmenu button.type-object > .icon {
	background-position: -72px -24px;
}

.jsoneditor-contextmenu button.type-object:hover > .icon,
.jsoneditor-contextmenu button.type-object:focus > .icon,
.jsoneditor-contextmenu button.type-object.selected > .icon {
	background-position: -72px 0;
}

.jsoneditor-contextmenu button.type-array > .icon {
	background-position: -96px -24px;
}

.jsoneditor-contextmenu button.type-array:hover > .icon,
.jsoneditor-contextmenu button.type-array:focus > .icon,
.jsoneditor-contextmenu button.type-array.selected > .icon {
	background-position: -96px 0;
}

/*----------------------------------------------*/
/* jsoneditor.css
/*----------------------------------------------*/

.jsoneditor .field,
.jsoneditor .value,
.jsoneditor .readonly {
	border: 1px solid transparent;
	min-height: 16px;
	min-width: 32px;
	padding: 2px;
	margin: 1px;
	word-wrap: break-word;
	float: left;
}

/* adjust margin of p elements inside editable divs, needed for Opera, IE */
.jsoneditor .field p,
.jsoneditor .value p {
	margin: 0;
}

.jsoneditor .value {
	word-break: break-word;
}

.jsoneditor .readonly {
	min-width: 16px;
	color: gray;
}

.jsoneditor .empty {
	border-color: lightgray;
	border-style: dashed;
	border-radius: 2px;
}

.jsoneditor .field.empty {
	background-image: url("jsoneditor-icons-1.png" /*tpa=http://www.json.org.cn/tools/JSONEditorOnline2.0/misc/img/jsoneditor-icons.png*/);
	background-position: 0 -144px;
}

.jsoneditor .value.empty {
	background-image: url("jsoneditor-icons-1.png" /*tpa=http://www.json.org.cn/tools/JSONEditorOnline2.0/misc/img/jsoneditor-icons.png*/);
	background-position: -48px -144px;
}

.jsoneditor .separator {
	padding: 3px 0;
	vertical-align: top;
}

.jsoneditor .field[contenteditable=true]:focus,
.jsoneditor .field[contenteditable=true]:hover,
.jsoneditor .value[contenteditable=true]:focus,
.jsoneditor .value[contenteditable=true]:hover,
.jsoneditor .field.highlight,
.jsoneditor .value.highlight {
	background-color: #FFFFAB;
	border: 1px solid yellow;
	border-radius: 2px;
}

.jsoneditor .field.highlight-active,
.jsoneditor .field.highlight-active:focus,
.jsoneditor .field.highlight-active:hover,
.jsoneditor .value.highlight-active,
.jsoneditor .value.highlight-active:focus,
.jsoneditor .value.highlight-active:hover {
	background-color: #ffee00;
	border: 1px solid #ffc700;
	border-radius: 2px;
}

.jsoneditor button {
	width: 24px;
	height: 24px;
	padding: 0;
	margin: 0;
	border: none;
	cursor: pointer;
	background: transparent url("jsoneditor-icons-1.png" /*tpa=http://www.json.org.cn/tools/JSONEditorOnline2.0/misc/img/jsoneditor-icons.png*/);
}

.jsoneditor button.collapsed {
	background-position: 0 -48px;
}

.jsoneditor button.expanded {
	background-position: 0 -72px;
}

.jsoneditor button.contextmenu {
	background-position: -48px -72px;
}

.jsoneditor button.contextmenu:hover,
.jsoneditor button.contextmenu:focus,
.jsoneditor button.contextmenu.selected {
	background-position: -48px -48px;
}

.jsoneditor div.content *:focus {
	outline: none;
}

.jsoneditor div.content button:focus {
    /* TODO: nice outline for buttons with focus
    outline: #97B0F8 solid 2px;
    box-shadow: 0 0 8px #97B0F8;
    */
	background-color: #f5f5f5;
	outline: #e5e5e5 solid 1px;
}

.jsoneditor button.invisible {
	visibility: hidden;
	background: none;
}

div.jsoneditor {
	color: #1A1A1A;
	border: 1px solid #97B0F8;
	-moz-box-sizing: border-box;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	width: 100%;
	height: 100%;
	overflow: auto;
	position: relative;
	padding: 0;
}

.jsoneditor table.content {
	border-collapse: collapse;
	border-spacing: 0;
	width: 100%;
	margin: 0;
}

.jsoneditor div.outer {
	width: 100%;
	height: 100%;
	margin: -35px 0 0 0;
	padding: 35px 0 0 0;
	-moz-box-sizing: border-box;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	overflow: hidden;
}

.jsoneditor div.content {
	width: 100%;
	height: 100%;
	position: relative;
	overflow: auto;
}

.jsoneditor textarea.content {
	width: 100%;
	height: 100%;
	margin: 0;
	-moz-box-sizing: border-box;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	border: none;
	background-color: white;
	resize: none;
}

.jsoneditor tr.highlight {
	background-color: #FFFFAB;
}

.jsoneditor button.dragarea {
	background: url("jsoneditor-icons-1.png" /*tpa=http://www.json.org.cn/tools/JSONEditorOnline2.0/misc/img/jsoneditor-icons.png*/) -72px -72px;
	cursor: move;
}

.jsoneditor button.dragarea:hover,
.jsoneditor button.dragarea:focus {
	background-position: -72px -48px;
}

.jsoneditor tr,
.jsoneditor th,
.jsoneditor td {
	padding: 0;
	margin: 0;
}

.jsoneditor td {
	vertical-align: top;
}

.jsoneditor td.tree {
	vertical-align: top;
}

.jsoneditor .field,
.jsoneditor .value,
.jsoneditor td,
.jsoneditor th,
.jsoneditor textarea {
	font-family: droid sans mono, monospace, courier new, courier, sans-serif;
	font-size: 10pt;
	color: #1A1A1A;
}

/* {JSON.org.cn} */
#by-jsonlint {
	text-align: right;
}

/*----------------------------------------------*/
/* menu.css
/*----------------------------------------------*/

.jsoneditor .menu {
	width: 100%;
	height: 35px;
	padding: 2px;
	margin: 0;
	overflow: hidden;
	-moz-box-sizing: border-box;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	color: #1A1A1A;
	border-bottom: 1px solid #C1C1C1;
}

.jsoneditor .menu button {
	width: 26px;
	height: 26px;
	margin: 2px;
	padding: 2px;
	border-radius: 2px;
	border: 1px solid #AEC0F8;
	background: #D0F0EF url("jsoneditor-icons-1.png" /*tpa=http://www.json.org.cn/tools/JSONEditorOnline2.0/misc/img/jsoneditor-icons.png*/);
}

.jsoneditor .menu button:hover {
	background-color: #f0f2f5;
}

.jsoneditor .menu button:active {
	background-color: #ffffff;
}

.jsoneditor .menu button:disabled {
	background-color: #e3eaf6;
}

.jsoneditor .menu button.collapse-all {
	background-position: 0 -96px;
}

.jsoneditor .menu button.expand-all {
	background-position: 0 -120px;
}

.jsoneditor .menu button.undo {
	background-position: -24px -96px;
}

.jsoneditor .menu button.undo:disabled {
	background-position: -24px -120px;
}

.jsoneditor .menu button.redo {
	background-position: -48px -96px;
}

.jsoneditor .menu button.redo:disabled {
	background-position: -48px -120px;
}

.jsoneditor .menu button.compact {
	background-position: -72px -96px;
}

.jsoneditor .menu button.format {
	background-position: -72px -120px;
}

/* TODO: css for button:disabled is not supported by IE8 */

/*----------------------------------------------*/
/* searchbox.css
/*----------------------------------------------*/

.jsoneditor .search input,
.jsoneditor .search .results {
	font-family: arial, sans-serif;
	font-size: 10pt;
	color: #1A1A1A;
}

.jsoneditor .search {
	position: absolute;
	right: 2px;
	top: 2px;
}

.jsoneditor .search .frame {
	border: 1px solid #97B0F8;
	background-color: white;
	padding: 0 2px;
	margin: 0;
}

.jsoneditor .search .frame table {
	border-collapse: collapse;
}

.jsoneditor .search input {
	width: 120px;
	border: none;
	outline: none;
	margin: 1px;
}

.jsoneditor .search .results {
	color: #4d4d4d;
	padding-right: 5px;
	line-height: 24px;
}

.jsoneditor .search button {
	width: 16px;
	height: 24px;
	padding: 0;
	margin: 0;
	border: none;
	background: url("jsoneditor-icons-1.png" /*tpa=http://www.json.org.cn/tools/JSONEditorOnline2.0/misc/img/jsoneditor-icons.png*/);
	vertical-align: top;
}

.jsoneditor .search button:hover {
	background-color: transparent;
}

.jsoneditor .search button.refresh {
	width: 18px;
	background-position: -99px -73px;
}

.jsoneditor .search button.next {
	cursor: pointer;
	background-position: -124px -73px;
}

.jsoneditor .search button.next:hover {
	background-position: -124px -49px;
}

.jsoneditor .search button.previous {
	cursor: pointer;
	background-position: -148px -73px;
	margin-right: 2px;
}

.jsoneditor .search button.previous:hover {
	background-position: -148px -49px;
}
