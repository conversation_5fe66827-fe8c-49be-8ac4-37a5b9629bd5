<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<html>
<head>
	<meta http-equiv="content-type" content="text/html; charset=utf-8">
	<title> JSON在线编辑器V2.0</title>
	<link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />
	<link rel="icon" href="/favicon.ico" type="image/x-icon" />
	<meta name="keywords" content="JSON在线编辑器" />
	<meta name="description" content="JSON在线编辑器" />
	<link rel="stylesheet" type="text/css" href="core.css" tppabs="http://www.json.org.cn/misc/core.css" />
	<style type="text/css">
	*{
		font-size: 13px;
	}
	/* Content Wrapper */
	#content-wrapper{
		width: 100%;
		height: 100%;
		padding: 0px;
		padding-top: 18px;
		padding-bottom: 88px;
		margin-bottom: -85px;
		text-align: left;
		-moz-box-sizing: border-box;
		-webkit-box-sizing: border-box;
		box-sizing: border-box;
	}
	</style>
	<!-- JSON Editor Online -->
	<link rel="stylesheet" type="text/css" href="core-1.css" tppabs="http://www.json.org.cn/tools/JSONEditorOnline2.0/misc/core.css" />
	<script type="text/javascript" src="core.js" tppabs="http://www.json.org.cn/tools/JSONEditorOnline2.0/misc/core.js"></script>
</head>

<body>
<!-- Content Wrapper -->
<div id="content-wrapper">
	<div id="jsonformatter"></div>
	<div id="splitter"></div>
	<div id="jsoneditor"></div>
	<div class="clear"></div>
</div>
<div class="clear"></div>

<script type="text/javascript">
try{
	app.load();
	app.resize();
}
catch(e){
}
</script>

</div>
</body>
</html>
