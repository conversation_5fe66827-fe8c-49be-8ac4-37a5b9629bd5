<!--
 * @Author: weiying
 * @Date: 2024-09-12 13:08:00
 * @LastEditors: weiying
 * @LastEditTime: 2024-09-12 16:40:44
 * @Description: 
-->
<template>
  <div ref="dom" class="charts chart-pie"></div>
</template>

<script>
 import tdTheme from './theme.json'
import { on, off } from '@/libs/tools'
import echarts from 'echarts'
echarts.registerTheme('tdTheme', tdTheme)
export default {
  name: 'Chart<PERSON>ie',
  props: {
    value: Array,
    text: String,
    subtext: String
  },
  data () {
    return {
      dom: null
    }
  },
  methods: {
    init(){
      this.$nextTick(() => {
        let legend = this.value.map(_ => _.name)
        let option = {
          title: {
            text: this.text,
            subtext: this.subtext,
            x: 'center'
          },
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b} : {c} ({d}%)'
          },
          legend: {
            data: []
          },
          series: [
            {
              type: 'pie',
              radius: ['60%', '85%'],
              center: ['50%', '60%'],
              data: this.value,
              color: [ "#1d7dff","#1d7dff"],
              itemStyle: {
                normal : {
                  label : {
                    show : false   //隐藏标示文字
                  },
                  labelLine : {
                    show : false   //隐藏标示线
                  }
                },
                emphasis: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                },

              }
            }
          ]
        }
        this.dom = echarts.init(this.$refs.dom, 'tdTheme')
        this.dom.setOption(option)
        on(window, 'resize', this.resize)
      })
    },
    resize () {
      this.dom.resize()
    }
  },
  watch: {
    value: {
      handler (newVal, oldVal) {
        if (this.dom) {
          if (newVal) {
            this.init();
          }
        }
      },
      deep: true //对象内部属性的监听，关键。
    },
  },
  mounted () {
    this.init();
  },
  beforeDestroy () {
    off(window, 'resize', this.resize)
  }
}
</script>
