.main{
  .logo-con{
    height: 64px;
    padding: 10px;
    text-align: center;
    line-height: 40px;
    background: #1677ff;
    color: #fff;
    line-height: 40px;
    img{
      height: 44px;
      width: auto;
      display: block;
      margin: 0 auto;

    }
  }
  .header-con{
    background: #1677ff;
    padding: 0 20px;
    width: 100%;
    color: #fff;
    height: 64px;
  }
  .main-layout-con{
    height: 100%;
    overflow: hidden;
  }
  .main-content-con{
    height: calc(100% - 60px);
    overflow: hidden;
  }
  .tag-nav-wrapper{
    padding: 0;
    height:40px;
    background:#F0F0F0;
  }
  .content-wrapper{
    padding: 18px;
    height: calc(100% - 80px);
    overflow: auto;
  }
  .left-sider{
    .ivu-layout-sider-children{
      overflow-y: scroll;
      margin-right: -18px;
    }
  }
}

.ivu-menu-item.ivu-menu-item-active .ivu-menu-item-selected{
  color: #515a6e !important;
  background: rgba(32, 176, 255, 0.1) !important;
}

.ivu-menu-dark.ivu-menu-vertical .ivu-menu-child-item-active > .ivu-menu-submenu-title {
  color: #515a6e !important;
}
.ivu-menu-dark.ivu-menu-vertical .ivu-menu-item:hover, .ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu-title:hover {
  //color: #515a6e !important;
  font-weight: bold;
  background: transparent !important;
}

.ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu .ivu-menu-item:hover {
  color: #515a6e ;
  font-weight: bold;
  background: transparent !important;
}

.ivu-menu-item > i{
  margin-right: 12px !important;
}
.ivu-menu-submenu > .ivu-menu > .ivu-menu-item > i {
  margin-right: 8px !important;
}
.collased-menu-dropdown{
  width: 100%;
  margin: 0;
  line-height: normal;
  padding: 7px 0 6px 16px;
  clear: both;
  font-size: 12px !important;
  white-space: nowrap;
  list-style: none;
  cursor: pointer;
  transition: background 0.2s ease-in-out;
  &:hover{
    background: rgba(100, 100, 100, 0.1);
  }
  & * {
    color: #515a6e;
  }
  .ivu-menu-item > i{
    margin-right: 12px !important;
  }
  .ivu-menu-submenu > .ivu-menu > .ivu-menu-item > i {
    margin-right: 8px !important;
  }
}

.ivu-select-dropdown.ivu-dropdown-transfer{
  max-height: 400px;
}


.ivu-table-border th, .ivu-table-border td {
  border: none;
  /* border-right: 1px solid #e8eaec; */
}




.ivu-page-options-elevator {
  display: inline-block;
  vertical-align: middle;
  height: 32px;
  line-height: 32px;
  color: #fff;
}
.ivu-table {
  width: inherit;
  height: 100%;
  max-width: 100%;
  overflow: hidden;
  color: #054303;
  font-size: 14px;
  //background-color: #275A7C !important;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.ivu-table th {
  height: 40px;
  font-size: 14px !important;
  white-space: nowrap;
  overflow: hidden;
  border: none !important;
  background-color: #f1f1f1 !important;
  color: #054303 !important;
}


.ivu-table td {
  font-size: 13px;
  color: #054303 !important;
  border: none !important;
  -webkit-transition: background-color 0.2s ease-in-out;
  transition: background-color 0.2s ease-in-out;
}

.ivu-table  .ivu-table-cell  strong {
  color: #134354 !important ;
}

a {
  color: #054303 !important;
  kground: transparent;
  text-decoration: none;
  outline: none;
  cursor: pointer;
  -webkit-transition: color 0.2s ease;
  transition: color 0.2s ease;
}

.ivu-btn-warning {
  font-size: 13px !important;
  color: #fff !important;
  background-color: #054303 !important;
  border-color: #054303 !important;
}

.tags-nav {
  position: relative;
  background-color: mix(#f1f2f3, #f3f4f5,10%) ;
  border-top: 1 px solid #F0F0F0;
  border-bottom: 1 px solid #F0F0F0;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 100%;
  height: 100%;
}

.tags-nav .ivu-tag-primary.ivu-tag-dot .ivu-tag-dot-inner {
  color: #fff !important;
  font-size:16px;
}
.ivu-table-stripe .ivu-table-body tr:nth-child(2n) td, .ivu-table-stripe .ivu-table-fixed-body tr:nth-child(2n) td {
  background-color: mix(@primary-color,#030233,90%);
}
.header-bar .sider-trigger-a .ivu-icon {
  color: #fff;
}
.tags-nav .ivu-tag-closable  {
  border-bottom: 1px solid #1677ff;
}
.side-menu-wrapper .ivu-menu-dark {
}

.side-menu-wrapper .ivu-menu-dark.ivu-menu-vertical .ivu-menu-opened {
}

.side-menu-wrapper .ivu-menu-dark.ivu-menu-vertical .ivu-menu-opened .ivu-menu-submenu-title {
}

 .main .ivu-menu-dark.ivu-menu-vertical .ivu-menu-item, .main .ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu-title {
  color: #000 ;
}

.side-menu-wrapper .ivu-menu-dark.ivu-menu-vertical .ivu-menu-item:hover, .main .ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu-title:hover {
  color: #1677ff;
  background-image: linear-gradient(to right, #ffffff1f , #ffffff87) !important;
  background-color: unset !important;
}

.side-menu-wrapper .ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu .ivu-menu-item:hover {
  color: #1677ff !important;
  background-color: rgba(0, 0, 0, 0.1) !important;
}

.side-menu-wrapper .ivu-menu-dark.ivu-menu-vertical .ivu-menu-opened .ivu-menu-submenu-title {
  color: #000 !important;
}

.side-menu-wrapper .ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu .ivu-menu-item-active {
  border-right: none;
  color: #f1f2f3;
  background: linear-gradient(to right, #ffffff1f , #ffffff87) !important;
}

.side-menu-wrapper .ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu  .ivu-menu-item-selected, .ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu .ivu-menu-item-active:hover {
  border-right: none;
  color: #1677ff;
  background: rgba(0, 0, 0, 0.1) !important;
  border-left: 10px solid #1677ff;
}


.ivu-page-item-jump-prev, .ivu-page-item-jump-next {
  border: 1px solid #dcdee2 !important;
  color: #ccc !important;
}

.ivu-page-item-jump-prev i, .ivu-page-item-jump-next i {
  color: #1d4b0b !important;
  display: inline !important;
}

//.ivu-layout-header {
//  background: #515a6e !important;
//  padding: 0 50px !important;
//  height: 28px !important;
//  line-height: 34px !important;
//}



.ivu-btn-text {
  border-color: #ccc !important;
}