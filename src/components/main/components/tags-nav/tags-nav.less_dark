.no-select{
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.size{
  width: 100%;
  height: 100%;
}
.tags-nav{
  position: relative;
  border-top: 1px solid #275A7C;
  border-bottom: 1px solid #275A7C;
  .no-select;
  .size;
  .close-con{
    position: absolute;
    right: 0;
    top: 0;
     height: 100%;
    width: 32px;
    background: #275A7C;
    text-align: center;
    z-index: 10;
  }
  .ivu-icon {
    display: inline-block;
    font-family: "Ionicons";
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    text-rendering: optimizeLegibility;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    vertical-align: -0.125em;
    text-align: center;
    color: #fff;
  }
  .btn-con{
    position: absolute;
    top: 0px;
    height: 100%;
    color: #f1f1f1;
    background: #275A7C;
    padding-top: 3px;
    z-index: 10;

    button{
      padding: 6px 4px;
      line-height: 14px;
      text-align: center;
    }
    &.left-btn{
      left: 0px;
    }
    &.right-btn{
      right: 32px;

      border-right: 1px solid tint(#275A7C,55%);
    }
  }
  .scroll-outer{
    position: absolute;
    left: 28px;
    right: 61px;
    top: 0;
    bottom: 0;
    box-shadow: 0px 0 3px 2px rgba(100,100,100,.1) inset;
    .scroll-body{
      height: ~"calc(100% - 1px)";
      display: inline-block;
      padding: 1px 4px 0;
      position: absolute;
      overflow: visible;
      white-space: nowrap;
      transition: left .3s ease;
      .ivu-tag-text {
        color: #f1f1f1 !important;
      }
      .ivu-tag-dot .ivu-icon-ios-close {
        color: #fff!important;
        margin-left: 12px !important;
      }
      .ivu-tag-dot {
        height: 32px;
        line-height: 32px;
        border: 1px solid #236c7c !important;
        background: #275A7C !important;
        padding: 0 12px;
      }
      .ivu-tag-dot-inner{
        transition: background .2s ease;
      }
    }
  }
  .contextmenu {
    position: absolute;
    margin: 0;
    padding: 5px 0;
    background: #fff;
    z-index: 1000;
    list-style-type: none;
    border-radius: 4px;
    box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, .1);
    li {
      margin: 0;
      padding: 5px 15px;
      cursor: pointer;
      &:hover {
        background: #217c5b;
      }
    }
  }
  .ivu-tag-primary.ivu-tag-dot {
    .ivu-tag-dot-inner {
      background-color:@primary-color
    }
  }
}
