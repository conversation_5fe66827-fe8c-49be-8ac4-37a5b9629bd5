<template>
  <Modal
    v-model="modal"
    width="1000"
    :title="title"
    @on-ok="repoOk"
    @on-cancel="repoCancel"
  >
    <div class="demo-split">
      <Split v-model="split1">
        <div slot="left" class="demo-split-pane">
          <Card shadow>
            <p slot="title">基本信息</p>
            <Form :label-width="120">
              <FormItem label="任务名称">
                {{row.name}}
              </FormItem>
              <FormItem label="任务类型">
                {{row.logType}}
              </FormItem>
              <FormItem label="开始时间">
                {{row.startTime}}
              </FormItem>
              <FormItem label="结束时间">
                {{row.stopTime}}
              </FormItem>
              <FormItem label="运行结果">
                <span :style="{color: getResultColor(row.result)}">{{row.result || '未运行'}}</span>
              </FormItem>
            </Form>
          </Card>
        </div>
        <div slot="right" class="demo-split-pane" style="overflow: auto">
          <Card shadow>
            <p slot="title">
              <Icon type="ios-document-outline" />
              详细日志信息
              <Button type="primary" size="small" style="float: right" @click="refreshLog">
                <Icon type="md-refresh" />
                刷新
              </Button>
            </p>
            <Spin size="large" fix v-if="spinShow"></Spin>
            <Input v-model="logText" type="textarea" :rows="18"
                   style="overflow-y:scroll;font-family: 'Courier New', monospace;"
                   placeholder="正在加载日志信息..." readonly />
          </Card>
        </div>
      </Split>
    </div>
  </Modal>
</template>

<script>
import util from '@/libs/util.js';
import config from '@/config/config';

export default {
  name: "log-detail-modal",
  props: {
    logDetailModal: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      modal: false,
      split1: 0.3,
      logText: '',
      spinShow: false
    }
  },
  computed: {
    title() {
      return `日志详情 - ${this.row.name || '未知任务'}`
    }
  },
  watch: {
    logDetailModal(val) {
      this.modal = val
      if (val) {
        this.loadLogDetail()
      }
    }
  },
  methods: {
    getResultColor(result) {
      if (!result || result === '' || result === 'null') return '#999'
      if (result === '成功') return '#19be6b'
      if (result === '失败') return '#ed4014'
      if (result === '运行中') return '#2d8cf0'
      return '#ff9900'
    },
    loadLogDetail() {
      if (!this.row.logId) return

      this.spinShow = true
      this.logText = '正在加载日志信息...'

      util.ajax.get(config.xtlServerContext + "/op/xlog/getLogContent", {
        params: {
          logId: this.row.logId
        }
      }).then((resp) => {
        if (resp.data.code === 11000) {
          this.logText = resp.data.data || '暂无详细日志信息'
        } else {
          this.logText = '获取日志信息失败'
        }
        this.spinShow = false
      }).catch((err) => {
        this.logText = '获取日志信息异常: ' + err
        this.spinShow = false
      })
    },
    refreshLog() {
      this.loadLogDetail()
    },
    repoOk() {
      this.$emit('on-ok')
    },
    repoCancel() {
      this.$emit('on-cancel')
    }
  }
}
</script>

<style scoped>
.demo-split {
  height: 500px;
  border: 1px solid #dcdee2;
}
.demo-split-pane {
  padding: 10px;
}
</style>



