<template>
  <Modal
    v-model="modal"
    fullscreen
    :title="title"
    @on-ok="repoOk"
    @on-cancel="repoCancel"
>

    <div class="demo-split">
      <Split v-model="split1">
        <div slot="left" class="demo-split-pane">
          <img  :src="src" width="100%" height="100%"
               scrolling="no" frameborder="0">
          </img>
        </div>
        <div slot="right" class="demo-split-pane" style="overflow: auto">
          <Card shadow>
            <p slot="title">当前作业实时调度日志</p>
            <p slot="title" style="position: absolute;margin: -5px auto auto -40px">
              <Icon type="ios-refresh" style="font-size: 24px;cursor: pointer"  @click="doRefreshByInterval"  />
            </p>
            <Input v-model="text" type="textarea" :rows="22"
                   style="overflow-y:scroll" placeholder="Enter something..." />
            <Spin fix v-if="spinShow">
              <Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
              <div>Loading</div>
            </Spin>
          </Card>
        </div>


      </Split>
    </div>


  </Modal>
 </template>
<script>
  import config from '@/config/config'
  import util from '@/libs/util.js';

  export default {
    name: "image-modal",

    props: {
      imgModal: {
        type: Boolean,
        default: false
      },
      row:{
        type: Object,
        default: ''
      },
      logText:{
        type: String,
        default:''
      },
      baseImg:{
        type: String,
        default:''
      }
    },
    data() {
      return {
        modal:false,
        text:"",
        timer:'',
        spinShow: false,
        split1: 0.5,

       }
    },

    created() {

      },
    mounted () {

    },
    computed:{
      src() {
        if(!this.row.jobId){
          return ;
        }
        let src = config.xtlServerContext+"/api/xjob/getJobImage?jobId="+this.row.jobId
        return src
      },

      title(){
        return "查看作业【"+this.row.jobName+"】调度图"
      }
    },
    watch:{
      imgModal(val) {
        this.modal = val
       }
    },
    methods: {
      doRefreshByInterval(){
        clearInterval(this.timer)
        this.doRefresh()
        if(this.modal){
          this.timer = setInterval(this.doRefresh,5000);
        }
      },
      doRefresh(){
        let self = this ;
        self.spinShow=true ;
        util.ajax.get(config.xtlServerContext+"/api/xjob/getLogText",{
          params:{
            jobId:this.row.jobId
          }
        }).then(function(resp) {
          let data = resp.data.data;
          self.text = data ;
          self.spinShow=false ;
        }).catch((err) => {
          this.$Message.error("获取调度日志出错,错误信息:" + err);
        })
      },
      repoOk() {
        this.modal = true
        this.$emit('on-ok')
        clearInterval(this.timer)
      },
      repoCancel() {
        this.$emit('on-cancel');
        clearInterval(this.timer)
      }
    }
    ,
    beforeDestroy() {
      clearInterval(this.timer);
    }
  }
</script>

<style scoped>

  .demo-split{
    height: 100%;
    border: 1px solid #dcdee2;
  }
  .demo-split-pane{
    padding: 10px;
  }

</style>
