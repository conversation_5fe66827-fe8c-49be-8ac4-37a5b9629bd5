<template>
  <xtl-page hasExtend hasBottom>
    <div slot="extend">
      <Button type="default" @click="pageGoBack">返回</Button>
    </div>
    <Row> &nbsp </Row>
    <xtl-search @do-search="doSearch"></xtl-search>

    <!-- 文件上传弹窗 -->
    <Modal v-model="uploadModalVisible" title="参数文件上传" width="500">
      <div>
        <input type="file" ref="fileInput" @change="handleFileUpload" accept=".properties,.txt" style="display: none;">
        <Button type="dashed" icon="md-add" @click="$refs.fileInput.click()" long>
          点击选择文件 (支持 properties/TXT 格式)
        </Button>
        <div v-if="uploadFileName" style="margin-top: 10px;">
          已选择文件: {{ uploadFileName }}
        </div>
      </div>
      <div slot="footer">
        <Button @click="uploadModalVisible = false">取消</Button>
        <Button type="primary" @click="parseFile" :loading="parseLoading" :disabled="!selectedFile">解析并导入</Button>
      </div>
    </Modal>

    <Collapse accordion v-model="value2">
      <Panel name="2" hide-arrow>
        参数信息
        <div slot="content">
          <Row justify=end>
            <i-col offset="14" span="3">
              <Button type="primary" icon="md-cloud-upload" @click="showUploadModal" size="small">文件上传</Button>
            </i-col>
            <i-col span="3">
              <Button type="primary" size="small" icon="md-add" @click="addrowData">增加一行</Button>
            </i-col>
          </Row>
          <Row>
            <xtlEditTable ref="table1" v-model="datas" :columns="columns" :showTableOption="showTableOption">
            </xtlEditTable>
          </Row>
          <!-- 分页组件 -->
          <Row v-if="totalNumber > 0" style="margin-top: 10px;">
            <i-col span="24" style="text-align: right;">
              <Page
                :total="totalNumber"
                :current="currentPage"
                :page-size="pageSize"
                :page-size-opts="pageSizeOpts"
                show-sizer
                show-total
                show-elevator
                @on-change="onPageChange"
                @on-page-size-change="onPageSizeChange">
              </Page>
            </i-col>
          </Row>
          <Row>
            &nbsp
          </Row>
          <Row>
            <i-col offset="10" span="2">
              <Button type="primary" @click="submit">确定</Button>
            </i-col>
            <i-col span="1">
              <Button type="default" @click="reset">重置</Button>
            </i-col>
          </Row>
        </div>
      </Panel>
    </Collapse>
  </xtl-page>
</template>

<script>
import util from '@/libs/util.js';
import config from '@/config/config';
import ICol from "view-design/src/components/grid/col";
import { getArrayFromFile } from '@/libs/platformUtil.js';

const deleteButton = (vm, h, currentRow, index) => {
  return h('Button', {
    props: {
      type: "error",
      ghost: true,
    },
    style: {
      margin: '0 5px'
    },
    on: {
      'click': () => {
        vm.doDelete(currentRow.objKey, index);
      }
    }
  }, '删除');
};
export default {
  components: { ICol },
  data() {
    return {
      contentType: 'table',
      form: {
        id: '',
        jobId: '',
        jobName: '',
        jobDescription: '',
        jobType: '',
        jobPath: '',
        jobRepositoryId: '',
        jobLogLevel: '',
        isDel: 0,
        isMonitorEnabled: false
      },
      tableData: [],
      imgData: [],
      tempList: [],
      fileData: [],
      input1: "",
      showTableOption: false,

      value1: "1",
      value2: "2",
      datas: [],
      uploadModalVisible: false,
      selectedFile: null,
      uploadFileName: '',
      parseLoading: false,
      
      // 分页相关数据
      currentPage: 1,
      pageSize: 10,
      pageSizeOpts: [10, 20, 50, 100],
      totalNumber: 0,
      allDatas: [], // 存储所有数据
    }
  },
  created() {

  },

  computed: {

    columns() {
      let columns = [];
      let self = this;
      columns.push({
        title: '参数编码',
        align: 'center',
        key: 'objCode',
        editor: {
          type: "text",
        }
      });
      columns.push({
        title: '中文名称',
        align: 'center',
        key: 'objName',
        editor: {
          type: "text",
        }
      });
      columns.push({
        title: '参数取值',
        align: 'center',
        key: 'objVal',
        editor: {
          type: "text",
        }
      });
      columns.push({
        title: '参数描述',
        align: 'center',
        key: 'objDes',
        editor: {
          type: "text",
        }
      });

      columns.push({
        title: '编辑',
        align: "center",
        render: function (h, param) {
          return h('div', [
            deleteButton(self, h, param.row, param.index)
          ]);
        }
      });
      return columns;
    },
    title: function () {
      return this.$route.meta.title;
    }
  },
  methods: {
    initData: function () {
      let self = this;

      util.ajax.get(config.xtlServerContext + '/op/xjob/get', {
        params: {
          jobId: this.$route.query.jobId
        }
      }).then(function (resp) {
        var result = resp.data.data
        self.form.id = result.id
        self.form.jobId = result.jobId
        self.form.jobName = result.jobName
        self.form.jobDescription = result.jobDescription
        self.form.jobType = result.jobType
        self.form.jobPath = result.jobPath
        self.form.jobRepositoryId = result.jobRepositoryId
        self.form.jobLogLevel = result.jobLogLevel
        let isDel = result.isDel
        if (isDel == 'null' || isDel == '' || isDel == '0') {
          self.form.isDel = 0
        } else {
          self.form.isDel = 1
        }
        let isMonitorEnabled = result.isMonitorEnabled
        if (isMonitorEnabled == '1') {
          self.form.isMonitorEnabled = true
        } else {
          self.form.isMonitorEnabled = false
        }
      }).catch((err) => {
        this.$Message.error("获取作业基本信息异常,错误信息:" + err);
      })

      util.ajax.get(config.xtlServerContext + "/op/xparams/getParams", {
        params: {
          targetId: this.$route.query.jobId,
          targetType: 'job'
        }
      }).then(function (resp) {
        self.allDatas = resp.data.data;
        self.totalNumber = self.allDatas.length;
        self.updatePaginatedData();
      }).catch((err) => {
        this.$Message.error("获取变量参数异常,错误信息:" + err);
      })
    },

    // 更新分页数据
    updatePaginatedData() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      this.datas = this.allDatas.slice(start, end);
    },

    // 页码变化处理
    onPageChange(page) {
      this.currentPage = page;
      this.updatePaginatedData();
    },

    // 每页大小变化处理
    onPageSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.currentPage = 1; // 重置到第一页
      this.updatePaginatedData();
    },

    doSearch() {
      this.initData()
    },

    pageGoBack: function () {
      this.$router.go(-1);
    },
    addrowData() {
      let row = {
        objCode: '',
        objName: '',
        objVal: '',
        objDes: ''
      }
      this.allDatas.push(row);
      this.totalNumber = this.allDatas.length;
      this.updatePaginatedData();
    },
    doDelete: function (cellId, index) {
      var self = this;
      this.$Modal.confirm({
        title: '提示',
        content: '<p>确定要删除吗?</p>',
        onOk: () => {
          // 计算在 allDatas 中的实际索引
          const actualIndex = (this.currentPage - 1) * this.pageSize + index;
          self.allDatas.splice(actualIndex, 1);
          self.totalNumber = self.allDatas.length;
          self.updatePaginatedData();
        }
      });
    },
    submit() {
      let self = this

      let param = {
        targetId: this.$route.query.jobId,
        targetType: 'job',
        datas: JSON.stringify(this.allDatas) // 提交所有数据
      }
      if (this.allDatas.length > 0) {
        util.ajax.post(config.xtlServerContext + "/op/xparams/add", param).then(function (resp) {
          let data = resp.data;
          if (data.code === 11000) {
            self.$Message.success("保存成功！");
            // 保存成功后默认查询一次
            self.doSearch();
          } else {
            self.$Message.error("保存失败!");
          }
        }).catch(function (err) {
          console.error(err);
          self.$Message.error("保存失败,请联系系统管理员");
        });
      } else {
        this.$Message.error("保存失败,请至少增加一条参数值！");
      }
    },
    reset() {
      this.allDatas = [];
      this.totalNumber = 0;
      this.currentPage = 1;
      this.updatePaginatedData();
    },
    showUploadModal() {
      this.uploadModalVisible = true;
    },
    handleFileUpload(event) {
      const file = event.target.files[0];
      if (file) {
        this.selectedFile = file;
        this.uploadFileName = file.name;
      }
    },
    // 文件导入接口方法
    importParamsFromFile(fileData) {
      let self = this;

      let param = {
        targetId: this.$route.query.jobId,
        targetType: 'job',
        fileData: JSON.stringify(fileData)
      };

      return util.ajax.post(config.xtlServerContext + "/op/xparams/importFromFile", param)
        .then(function (resp) {
          let data = resp.data;
          if (data.code === 11000) {
            self.$Message.success("文件导入成功！");
            self.doSearch(); // 导入成功后刷新数据
            return data;
          } else {
            self.$Message.error("文件导入失败: " + data.message);
            throw new Error(data.message);
          }
        })
        .catch(function (err) {
          console.error('文件导入失败:', err);
          self.$Message.error("文件导入失败，请联系系统管理员");
          throw err;
        });
    },
    async parseFile() {
      if (!this.selectedFile) {
        this.$Message.warning('请选择一个文件');
        return;
      }

      this.parseLoading = true;
      try {
        // 直接读取文件内容
        const fileContent = await this.readFileAsText(this.selectedFile);

        // 按行分割文件内容
        const lines = fileContent.split(/\r?\n/);

        // 过滤和解析有效行
        const validLines = [];
        for (let line of lines) {
          line = line.trim();
          // 跳过空行、注释行和KETTLE_开头的行
          if (line && !line.startsWith('#') && !line.startsWith('KETTLE_') && line.includes('=')) {
            validLines.push(line);
          }
        }

        if (validLines.length > 0) {
          // 调用导入接口，传递处理后的行数据
          await this.importParamsFromFile(validLines);
          this.uploadModalVisible = false;
        } else {
          this.$Message.error('文件中没有有效的参数数据');
        }
      } catch (error) {
        console.error('文件解析失败:', error);
        this.$Message.error('文件处理失败: ' + error.message);
      } finally {
        this.parseLoading = false;
      }
    },
    // 读取文件内容为文本
    readFileAsText(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = (e) => resolve(e.target.result);
        reader.onerror = (e) => reject(e);
        reader.readAsText(file, 'UTF-8');
      });
    },
    parseContent(content) {
      const lines = content.split('\n');
      const headers = lines[0].split(',');
      const parsedData = [];

      for (let i = 1; i < lines.length; i++) {
        const values = lines[i].split(',');
        if (values.length === headers.length) {
          const row = {};
          headers.forEach((header, index) => {
            row[header.trim()] = values[index].trim();
          });
          parsedData.push(row);
        }
      }

      return parsedData;
    },
  },
  mounted: function () {
    this.initData()
  }
}
</script>

<style></style>
