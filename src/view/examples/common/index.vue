<template>
    <xtl-page>
        <xtl-detail title="空页面框架" icon="ios-folder-open">
            &ltxtl-page&gt &lt/xtl-page&gt
            参考“分页示例页面”
        </xtl-detail>
        <xtl-detail title="明细页面框架" icon="ios-folder-open">
            &ltxtl-detail&gt &lt/xtl-detail&gt
            参考“分页示例页面-详情页面”
        </xtl-detail>
        <xtl-detail title="帮助框" icon="ios-folder-open">
            &ltxtl-help&gt &lt/xtl-help&gt
            参考“分页示例页面”
        </xtl-detail>
        <xtl-detail title="分页表格" icon="ios-folder-open">
            &ltxtl-table&gt &lt/xtl-table&gt
            参考“分页示例页面”
        </xtl-detail>
        <xtl-detail title="查询条件布局" icon="ios-folder-open">
            &ltxtl-search&gt &lt/xtl-search&gt
            参考“分页示例页面”
        </xtl-detail>
        <xtl-detail title="上传文件" icon="ios-folder-open">
            &ltxtl-upload-file&gt &lt/xtl-upload-file&gt
            参考“分页示例页面-编辑页面”
        </xtl-detail>
        <xtl-detail title="上传图片" icon="ios-folder-open">
            &ltxtl-upload-img&gt &lt/xtl-upload-img&gt
            参考“分页示例页面-编辑页面”
        </xtl-detail>
        <xtl-detail title="批量上传图片" icon="ios-folder-open">
            &ltxtl-upload-img-list&gt &lt/xtl-upload-img-list&gt
            参考“分页示例页面-编辑页面”
        </xtl-detail>
        <xtl-detail title="菜单框架页面" icon="ios-folder-open">
            &ltxtl-x1-frame&gt &lt/xtl-x1-frame&gt
            参考“examples/components/frame/frame.vue”
        </xtl-detail>
        <xtl-detail title="顶部导航" icon="ios-folder-open">
            &ltxtl-top-bar&gt &lt/xtl-top-bar&gt
            参考“packages/components/xtl-x1-frame/src/xtl-x1-frame.vue”
        </xtl-detail>
        <xtl-detail title="默认首页" icon="ios-folder-open">
            &ltxtl-home-page&gt &lt/xtl-home-page&gt
            参考“examples/components/homepage/homepage.vue”
        </xtl-detail>
        <xtl-detail title="登录页" icon="ios-folder-open">
            &ltxtl-login&gt &lt/xtl-login&gt
            参考“examples/components/login/login.vue”
        </xtl-detail>
        <xtl-detail title="自然周选择框" icon="ios-folder-open">
            &ltxtl-weeks&gt &lt/xtl-weeks&gt
            参考“分页示例页面”
        </xtl-detail>
        <xtl-detail title="编辑表格" icon="ios-folder-open">
            &ltxtl-edit-table&gt &lt/xtl-edit-table&gt
            参考“分页示例页面-编辑页面”
        </xtl-detail>
        <xtl-detail title="富文本编辑框" icon="ios-folder-open">
            &ltxtl-editor&gt &lt/xtl-editor&gt
            参考“分页示例页面-编辑页面”
        </xtl-detail>
        <xtl-detail title="可拖动查询条件" icon="ios-folder-open">
            &ltxtl-drag-search&gt &lt/xtl-drag-search&gt
            参考“XtlElTable示例”
        </xtl-detail>
        <xtl-detail title="合并行、合并列表格" icon="ios-folder-open">
            &ltxtl-el-table&gt &lt/xtl-el-table&gt
            参考“XtlElTable示例”
        </xtl-detail>
    </xtl-page>
</template>

<script>
    export default {
        name: "common-index",
        methods:{

        }
    }
</script>

<style scoped>

</style>
