<template>
  <div class="file-management-container">
    <!-- 搜索和操作按钮区域 -->
    <div class="search-action-row">


      <div class="action-section">
        <el-button type="primary" icon="el-icon-upload2" @click="showUploadDialog">
          上传管理
        </el-button>
        <el-button type="success" icon="el-icon-download" @click="handleBatchDownload()">
          批量下载
        </el-button>
        <el-button type="danger" icon="el-icon-delete" @click="BatchDelete()">
          批量删除
        </el-button>
      </div>
        <div class="search-section">
        <el-input
          v-model="searchQuery"
          placeholder="输入文件名进行搜索"
          class="search-input"
        />
        <el-button type="primary" @click="handleSearch" class="search-btn">查询</el-button>
      </div>
    </div>

    <!-- 上传管理小页面 -->
    <div v-if="uploadDialogVisible" class="upload-popup-overlay" @click="handleOverlayClick">
      <div class="upload-popup-container" @click.stop>
        <!-- 标题栏 -->
        <div class="popup-header">
          <div class="popup-title">
            <i class="el-icon-upload2"></i>
            上传管理
          </div>
          <el-button
            type="text"
            icon="el-icon-close"
            @click="handleUploadDialogClose"
            class="close-btn"
          />
        </div>

        <!-- 内容区域 -->
        <div class="popup-content">
          <!-- 路径设置 -->
          <div class="upload-section">
            <div class="section-header" style="display: flex; align-items: center;">
              <i class="el-icon-folder"></i>
              <span>设置上传路径 → </span> 
              <span style="color: #f5222d; font-weight: bold;"> 检查选择路径是否正确！</span>
              <el-button
                size="mini"
                type="text"
                @click="refreshUploadPaths"
                :loading="uploading"
                style="margin-left: 8px; color: #f5222d; display: flex; align-items: center; font-weight: bold;"
                title="刷新路径列表"
              >
                <i class="el-icon-refresh" style="font-size: 20px; margin-right: 4px;"></i>
              </el-button>
            </div>
            <div class="section-body">
              <el-select
                v-model="selectedUploadPath"
                placeholder="请选择上传路径"
                style="width: 100%;"
              >
                <el-option
                  v-for="path in uploadPaths"
                  :key="path"
                  :label="path"
                  :value="path"
                />
              </el-select>
              <div v-if="selectedUploadPath" class="selected-path">
                <i class="el-icon-location"></i>
                当前路径：{{ selectedUploadPath }}
              </div>
            </div>
          </div>

          <!-- 文件上传 -->
          <div class="upload-section">
            <div class="section-header">
              <i class="el-icon-document-add"></i>
              <span>选择文件</span>
            </div>
            <div class="section-body">
              <el-upload
                class="compact-upload"
                ref="dialogUpload"
                action=""
                :http-request="customRequest"
                :on-remove="handleRemove"
                :file-list="tempFileList"
                multiple
                accept=".ktr,.kjb,.zip"
                :auto-upload="false"
                :on-change="handleFileChange"
                drag
              >
                <div class="upload-area-compact">
                  <i class="el-icon-plus"></i>
                  <div class="upload-text">点击或拖拽文件到此处</div>
                  <div class="upload-hint">支持 .ktr/.kjb/.zip 格式</div>
                </div>
              </el-upload>

              <!-- 文件列表 -->
              <div v-if="tempFileList.length > 0" class="compact-file-list">
                <div class="file-list-header">
                  <span>已选择文件 ({{ tempFileList.length }})</span>
                </div>
                <div class="file-items">
                  <div v-for="(file, index) in tempFileList" :key="index" class="compact-file-item">
                    <i class="el-icon-document"></i>
                    <span class="file-name">{{ file.name }}</span>
                    <span class="file-size">({{ formatFileSize(file.size) }})</span>
                    <el-button
                      type="text"
                      icon="el-icon-delete"
                      @click="removeFile(index)"
                      class="remove-file-btn"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 底部操作栏 -->
        <div class="popup-footer">
          <div class="footer-left">
            <span v-if="tempFileList.length > 0" class="file-summary">
              共 {{ tempFileList.length }} 个文件待上传
            </span>
          </div>
          <div class="footer-right">
            <el-button size="small" @click="handleUploadDialogClose">取消</el-button>
            <el-button
              type="primary"
              size="small"
              @click="confirmUpload"
              :disabled="!selectedUploadPath || tempFileList.length === 0"
              :loading="uploading"
            >
              {{ uploading ? '上传中...' : '开始上传' }}
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <el-table :data="paginatedFileList" style="width: 100%" border ref="table">
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column prop="fid" label="ID" width="120"></el-table-column>
      <el-table-column prop="name" label="文件名称" width="320"></el-table-column>
      <el-table-column prop="size" label="文件大小" width="120"></el-table-column>
      <el-table-column prop="path" label="文件路径" width="420"></el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="180"></el-table-column>
    </el-table>



    <!-- 使用 flex 布局将总数和分页放在同一行 -->
    <div style="display: flex; justify-content: space-between; align-items: center; margin: 20px 0;">
      <span class="span">共 {{ totalItems }} 条</span>
      <el-pagination
       background
        :total="totalItems" :current-page.sync="currentPage"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" layout="prev, pager, next">
      </el-pagination>
    </div>

  </div>


</template>

<style scoped>
.file-management-container {
  padding: 20px;
  background: #f5f5f5;
}

/* 搜索和操作按钮行样式 */
.search-action-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: white;
  padding: 15px;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.search-section {
  display: flex;
  align-items: center;
}

.search-input {
  width: 300px;
  margin-right: 10px;
}

.search-btn {
  margin-right: 0;
}

.action-section {
  display: flex;
  align-items: center;
}

.action-section .el-button {
  margin-left: 10px;
}

/* 上传管理小页面样式 */
.upload-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeIn 0.3s ease;
}

.upload-popup-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  width: 500px;
  max-width: 90vw;
  max-height: 80vh;
  overflow: hidden;
  animation: slideIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 标题栏 */
.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #409eff;
  color: white;
}

.popup-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
}

.popup-title i {
  margin-right: 8px;
}

.close-btn {
  color: white !important;
  font-size: 18px;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.1) !important;
}

/* 内容区域 */
.popup-content {
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
}

.upload-section {
  margin-bottom: 20px;
}

.upload-section:last-child {
  margin-bottom: 0;
}

.section-header {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: bold;
  color: #333;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e9ecef;
}

.section-header i {
  margin-right: 6px;
  color: #409eff;
}

.section-body {
  padding-left: 20px;
}

.selected-path {
  margin-top: 10px;
  padding: 8px 12px;
  background: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
  font-size: 13px;
  color: #409eff;
}

.selected-path i {
  margin-right: 5px;
}

/* 紧凑上传区域 */
.compact-upload {
  width: 100%;
}

.upload-area-compact {
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  padding: 30px 20px;
  text-align: center;
  background: #fafafa;
  transition: all 0.3s ease;
  cursor: pointer;
}

.upload-area-compact:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.upload-area-compact i {
  font-size: 24px;
  color: #c0c4cc;
  margin-bottom: 8px;
}

.upload-text {
  color: #333;
  font-size: 14px;
  margin-bottom: 4px;
}

.upload-hint {
  color: #999;
  font-size: 12px;
}

/* 紧凑文件列表 */
.compact-file-list {
  margin-top: 15px;
  border-top: 1px solid #e9ecef;
  padding-top: 15px;
}

.file-list-header {
  font-size: 13px;
  font-weight: bold;
  color: #666;
  margin-bottom: 10px;
}

.file-items {
  max-height: 150px;
  overflow-y: auto;
}

.compact-file-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  margin-bottom: 6px;
  font-size: 13px;
}

.compact-file-item i {
  color: #409eff;
  margin-right: 8px;
}

.compact-file-item .file-name {
  flex: 1;
  color: #333;
  font-weight: 500;
  margin-right: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.compact-file-item .file-size {
  color: #999;
  font-size: 12px;
  margin-right: 8px;
}

.remove-file-btn {
  color: #f56c6c;
  font-size: 14px;
  padding: 2px;
}

.remove-file-btn:hover {
  background: #fef0f0;
}

/* 底部操作栏 */
.popup-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.footer-left {
  font-size: 13px;
  color: #666;
}

.file-summary {
  color: #409eff;
  font-weight: 500;
}

.footer-right {
  display: flex;
  gap: 8px;
}

/* 其他样式 */
.span {
  color: #1677ff;
}

.el-table {
  margin-top: 0;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.pagination {
  margin-top: 20px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .search-action-row {
    flex-direction: column;
    align-items: flex-start;
  }

  .search-section {
    margin-bottom: 15px;
    width: 100%;
  }

  .search-input {
    width: 100%;
    margin-bottom: 10px;
    margin-right: 0;
  }

  .action-section {
    width: 100%;
    justify-content: flex-start;
  }

  .action-section .el-button {
    margin-left: 0;
    margin-right: 10px;
    margin-bottom: 5px;
  }
}
</style>

<script>


import axios from 'axios';
import util from '@/libs/util.js';
import config from '@/config/config';

export default {
  data() {
    return {
      uploadUrl: '',
      fileList: [],
      searchQuery: '',
      currentPage: 1,
      pageSize: 10,
      totalItems: 0,     // 数据总数
      uploadPath: '', // 当前选择的上传路径
      uploadPaths: [], // 假设有多个路径选项
      uploadDialogVisible: false, // 上传对话框显示状态
      selectedUploadPath: '', // 对话框中选择的上传路径
      tempFileList: [], // 临时文件列表
      uploading: false, // 上传状态
    };
  },
  computed: {
    filteredFileList() {
      if (!this.searchQuery) {
        return this.fileList;
      }
      return this.fileList.filter(file => {
        return file.name.includes(this.searchQuery);
      });
    },
    paginatedFileList() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      return this.filteredFileList.slice(start, end);
    },
  },
  mounted() {
    this.handleSearch(); // 组件创建时加载数据
    this.selectedFiles();
  },
  methods: {
    doClear() {
        // 清理旧的上传组件引用（如果存在）
        if (this.$refs.upload) {
            this.$refs.upload.clearFiles();
        }
        // 清理弹窗中的上传组件引用（如果存在）
        if (this.$refs.dialogUpload) {
            this.$refs.dialogUpload.clearFiles();
        }
    },

    // 显示上传对话框
    showUploadDialog() {
      this.selectedUploadPath = this.uploadPath; // 默认选择当前路径
      this.tempFileList = [];
      this.uploadDialogVisible = true;
    },

    // 关闭上传对话框
    handleUploadDialogClose() {
      this.uploadDialogVisible = false;
      this.selectedUploadPath = '';
      this.tempFileList = [];
      this.uploading = false;
      if (this.$refs.dialogUpload) {
        this.$refs.dialogUpload.clearFiles();
      }
    },

    // 处理遮罩层点击
    handleOverlayClick() {
      this.handleUploadDialogClose();
    },

    // 处理文件选择变化
    handleFileChange(file, fileList) {
      this.tempFileList = fileList;
    },

    // 移除文件
    removeFile(index) {
      this.tempFileList.splice(index, 1);
      // 同步更新上传组件的文件列表
      if (this.$refs.dialogUpload) {
        this.$refs.dialogUpload.fileList = this.tempFileList;
      }
    },

    // 格式化文件大小
    formatFileSize(size) {
      const units = ['B', 'KB', 'MB', 'GB'];
      let index = 0;
      while (size >= 1024 && index < units.length - 1) {
        size /= 1024;
        index++;
      }
      return `${size.toFixed(2)} ${units[index]}`;
    },

    // 确认上传
    confirmUpload() {
      // 校验是否设置了上传路径
      if (!this.selectedUploadPath) {
        this.$Message.warning('请先设置上传路径！');
        return;
      }

      if (this.tempFileList.length === 0) {
        this.$Message.warning('请选择要上传的文件！');
        return;
      }

      // 设置上传状态
      this.uploading = true;

      // 更新当前上传路径
      this.uploadPath = this.selectedUploadPath;

      // 上传文件计数
      let uploadedCount = 0;
      let failedCount = 0;
      const totalFiles = this.tempFileList.length;

      // 开始上传文件
      this.tempFileList.forEach((fileItem) => {
        this.customRequest({
          file: fileItem,
          onProgress: () => {},
          onSuccess: () => {
            uploadedCount++;
            this.checkUploadComplete(uploadedCount, failedCount, totalFiles);
          },
          onError: (error) => {
            failedCount++;
            console.error(`文件 ${fileItem.name} 上传失败:`, error);
            this.checkUploadComplete(uploadedCount, failedCount, totalFiles);
          }
        });
      });
    },

    // 检查上传是否完成
    checkUploadComplete(uploadedCount, failedCount, totalFiles) {
      const completedCount = uploadedCount + failedCount;

      if (completedCount === totalFiles) {
        this.uploading = false;

        if (failedCount === 0) {
          this.$Message.success(`成功上传 ${uploadedCount} 个文件！`);
          this.handleUploadDialogClose();
          this.handleSearch(); // 刷新文件列表
        } else if (uploadedCount === 0) {
          this.$Message.error(`所有文件上传失败！`);
        } else {
          this.$Message.warning(`上传完成：成功 ${uploadedCount} 个，失败 ${failedCount} 个`);
          this.handleSearch(); // 刷新文件列表
        }
      }
    },
    selectedFiles() {
      const self = this;
      return util.ajax.get(config.xtlServerContext + "/api/xrepo/listFileRepoList?&offset=0&limit=10").then(function (resp) {
        let data = resp.data;
        if (data.code === 11000) {
          const rows = data.data.rows;
          rows.forEach(row => {
            if (row.baseDir) {
              self.uploadPaths.push(row.baseDir);
            }
          });
          self.uploadPath = self.uploadPaths[0];
        }
      }).catch((err) => {
        console.log('.err :>> ', err);
      });
    },

    // 处理页码变化
    handleCurrentChange(newPage) {
      this.currentPage = newPage;
      this.updatePaginatedData();
    },
    // 处理每页大小变化
    handleSizeChange(newSize) {
      this.pageSize = newSize;
      this.currentPage = 1; // 重置到第一页
      this.updatePaginatedData();
    },
    // 更新当前页的数据
    updatePaginatedData() {
      const start = (this.currentPage - 1) * this.pageSize;
      this.paginatedData = this.dataset.slice(start, start + this.pageSize);
    },
    customRequest({ file, onProgress, onSuccess, onError }) {
      let self = this;
      const formData = new FormData();

      formData.append('file', file.raw || file);
      formData.append('uploadPath', self.selectedUploadPath || self.uploadPath);

      util.ajax.post(config.xtlServerContext + "/api/xrepo/uploadFile", formData, true)
        .then(function (resp) {
          let data = resp.data;
          let code = data.code;
          if (code === 11000) {
            // 只在非弹窗上传时显示单个文件成功消息
            if (!self.uploadDialogVisible) {
              self.$Message.success(data.data);
            }
            self.fileList.push({
              name: file.name,
              size: self.formatSize(file.size),
              createTime: new Date().toLocaleString(),
            });
            // 调用成功回调
            if (onSuccess) {
              onSuccess(resp);
            }
          } else {
            // 处理其他情况
            if (!self.uploadDialogVisible) {
              self.$Message.error(data.message || '上传失败');
            }
            if (onError) {
              onError(new Error(data.message || '上传失败'));
            }
          }
          // 只在非弹窗上传时清理和刷新
          if (!self.uploadDialogVisible) {
            self.doClear();
            self.handleSearch();
          }
        })
        .catch(function (err) {
          // 错误处理
          if (!self.uploadDialogVisible) {
            self.$Message.error('上传请求失败');
          }
          if (onError) {
            onError(err);
          }
        });
    },
    handleChange(file, fileList) {
      console.log(JSON.stringify(file, null, 2));
    },
    handleRemove(file) {
      // 移除文件记录
      const index = this.fileList.indexOf(file);
      if (index >= 0) {
        this.fileList.splice(index, 1);
      }
    },
    handleCurrentChange(page) {
      this.currentPage = page;
    },
    handleSearch() {
      let self = this;
      this.totalItems = this.totalSearch();
      this.currentPage = 1;
      const formData = new FormData();
      formData.append('query', this.searchQuery);
      util.ajax.post(config.xtlServerContext + "/api/xrepo/selectList", formData, true)
        .then(function (resp) {
          let data = resp.data;
          let code = data.code;
          if (code === 11000) {
            self.fileList = []; // 清空已有数据
            data.data.forEach(item => {
              self.fileList.push({
                name: item.fileName, // 假设 item 包含 name 属性
                size: self.formatSize(item.fileSize), // 假设 item 包含 size 属性
                createTime: item.uploadTime.toLocaleString(), // 假设 item 包含 createTime 属性
                fid: item.id, // 假设 item 包含 id 属性
                path: item.filePath, // 假设 item 包含 filePath 属性
              });
            });
            self.totalItems = data.data.length;
            this.updatePaginatedData(); // 初始化分页数据
          } else {
            // 处理其他情况
          }
        })
        .catch(function (err) {
          // 错误处理
        });
    },

    totalSearch() {
      this.currentPage = 1;
      const formData = new FormData();
      formData.append('query', this.searchQuery);
      util.ajax.post(config.xtlServerContext + "/api/xrepo/selectList", formData, true)
        .then(function (resp) {
          let data = resp.data;
          let code = data.code;
          if (code === 11000) {
            return data.data.length
          } else {
          }
        }).catch(function (err) {
        });
    },
    formatSize(size) {
      const units = ['B', 'KB', 'MB', 'GB'];
      let index = 0;
      while (size >= 1024 && index < units.length - 1) {
        size /= 1024;
        index++;
      }
      return `${size.toFixed(2)} ${units[index]}`;
    },

    handleBatchDownload() {
      const selectedFiles = this.$refs.table.selection; // 获取选中的文件
      const fileIds = selectedFiles.map(file => file.fid); // 假设文件对象有个 fid 属性
      fileIds.forEach((fileId, index) => {
        setTimeout(() => {
          // 这里我们创建一个新链接触发下载
          window.location.href = `${config.xtlServerContext}/api/xrepo/downloadFiles/${fileId}`;
        }, index * 1000); // 每个下载延迟 1 秒，避免同时下载
      });

    },
    BatchDelete() {
      const selectedFiles = this.$refs.table.selection; // 获取选中的文件
      if (selectedFiles.length === 0) {
        this.$Message.success('请先选择文件进行操作!');
        return;
      }

      const fileIds = selectedFiles.map(file => file.name); // 假设文件对象有个 fid 属性
      console.log('Selected file IDs: ', fileIds);

      const formData = new FormData();
      fileIds.forEach(id => {
        formData.append('fileIds', id);
      });

      this.$Modal.confirm({
        title: '提示',
        content: '<p>确定要删除吗?</p>',
        onOk: () => {
          // 调用删除 API
          util.ajax.post(config.xtlServerContext + "/api/xrepo/deleteByFileName", formData, true)
            .then(resp => {
              const data = resp.data;
              if (data.code === 11000) {
                this.$Message.success(data.data);
                this.handleSearch(); // 删除成功后刷新文件列表
              } else {
                this.$Message.success('删除失败: ' + data.message);
              }
            })
            .catch(err => {
              this.$Message.success('删除请求失败');
            });

        }
      });

    },
    Delete() {
      const selectedFiles = this.$refs.table.selection; // 获取选中的文件
      if (selectedFiles.length === 0) {
        this.$Message.success('请先选择文件进行操作!');
        return;
      }
      const fileIds = selectedFiles.map(file => file.name); // 假设文件对象有一个 id 属性
      console.log('object :>> ', fileIds);

      const formData = new FormData();
      formData.append('fileName', fileIds[0]);
      // 调用删除 API
      util.ajax.post(config.xtlServerContext + "/api/xrepo/deleteByFileName", formData, true)
        .then(function (resp) {
          let data = resp.data;
          if (data.code === 11000) {
            this.$Message.success(data.data);
            this.handleSearch();
          }
        })
        .catch(err => {
          console.log('aaa :>> ', err);
        });

    },
    handleDownload(file) {
      axios.get(`${config.xtlServerContext}/api/xrepo/downloadFile`, {
        params: { fileId: file.id },
        responseType: 'blob',
      })
        .then(response => {
          const url = window.URL.createObjectURL(new Blob([response.data]));
          const link = document.createElement('a');
          link.href = url;
          link.setAttribute('download', file.name);
          document.body.appendChild(link);
          link.click();
          link.remove();
          window.URL.revokeObjectURL(url);
        })
        .catch(err => {
          this.$message.error('下载失败!');
        });
    },
    refreshUploadPaths() {
      this.uploadPaths = [];
      this.selectedFiles().then(() => {
        this.$Message.success('刷新成功！');
      });
    },
  },
};
</script>

<style>
/* 确保Message提示显示在最上层 */
.ivu-message {
  z-index: 9999 !important;
}
.ivu-message-notice {
  z-index: 9999 !important;
}
</style>
