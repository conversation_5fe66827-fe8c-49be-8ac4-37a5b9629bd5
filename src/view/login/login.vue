<!--
 * @Author: weiying
 * @Date: 2024-09-12 13:08:00
 * @LastEditors: weiying
 * @LastEditTime: 2024-09-12 16:19:16
 * @Description: 
-->
<style lang="less">
  @import './login.less';
</style>

<template>
  <div class="login">

    <div class="login-con">
      <Card icon="log-in"   :bordered="false">
        <div style="text-align:center">
          <span style=" font-weight: 500;    margin-bottom: 25px;    font-family: fantay;color:#0b4fe0;font-size: 23px"><h3>ETL调度管理平台</h3></span>
          </div>
        <div class="form-con">
          <login-form ref="loginForm" @on-success-valid="handleSubmit" :loading="loading"></login-form>
        </div>
      </Card>
    </div>
    <div style="text-align: center;  position: absolute;  width: 100%;  margin: 0 auto; bottom: 0;  padding: 12px; color: #000;    font-size: 12px;    background: #ffffff4a;">
      <a target="_blank" style="color: #000 !important;" href="#">
        © 2022-2026 规划、设计、开发、运维等版权归开发者所有</a>&nbsp;<br/>
    </div>
  </div>
</template>

<script>
import LoginForm from '_c/login-form'
import { mapActions, mapGetters } from 'vuex'
export default {
  components: {
    LoginForm
  },
  data () {
    return {
      value3: 0,
      loading: false,
      setting: {
        autoplay: true,
        autoplaySpeed: 2000,
        dots: 'inside',
        radiusDot: false,
        arrow: 'never'
      }
    }
  },
  methods: {
    ...mapGetters([
      'getToken'
    ]),
    ...mapActions([
      'handleLogin',
      'getUserInfo'
    ]),
    handleSubmit ({ userName, passWord ,verifyCode,uuid}) {
      const that = this
      that.loading = true
      that.handleLogin({ userName, passWord,verifyCode,uuid }).then(res => {
         if (that.getToken() != null ){
          this.$Message.success({
            closable: true,
            content: '登录成功',
            type: 'success'
          })
        }

        that.$router.push({
          name: that.$config.homeName
        })

         that.loading = false
      }).catch(err => {
        that.loading = false
        that.$refs.loginForm.fleshVerify()
        that.$Message.error(err.message)
      })
    },
  }
}
</script>

<style>

</style>
