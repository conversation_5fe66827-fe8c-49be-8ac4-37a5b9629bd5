<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="227px" height="269px" viewBox="0 0 227 269" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 46.2 (44496) - http://www.bohemiancoding.com/sketch -->
    <title>Group 9</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <path d="M0,131.5 C1.52216317e-15,101.788282 2.0023434,70.3064817 6.0070302,37.054599 L6.00703147,37.0545992 C8.23177711,18.5819983 23.2321456,4.27478563 41.7892683,2.9258328 C68.6224469,0.975277594 91.0407969,-6.24317082e-09 109.044318,0 C127.133265,6.27279111e-09 149.842798,0.984554771 177.17292,2.9536643 L177.17292,2.95366535 C195.745345,4.29179116 210.761581,18.6099912 212.981582,37.0977487 C216.993859,70.5112465 218.999998,102.082949 219,131.812857 C219.000002,161.472158 217.003381,192.843579 213.010137,225.927119 L213.010136,225.927119 C210.780269,244.401296 195.772997,258.705423 177.213148,260.046888 C149.974602,262.015629 127.477504,263 109.721856,263 C91.927568,263 69.2911098,262.01134 41.8124813,260.034021 L41.8124814,260.03402 C23.2430488,258.697792 8.22697111,244.385384 6.00173205,225.9014 C2.00057735,192.665746 1.52149174e-15,161.198612 0,131.5 Z" id="path-1"></path>
        <filter x="-4.5%" y="-1.4%" width="106.2%" height="104.4%" filterUnits="objectBoundingBox" id="filter-2">
            <feMorphology radius="0.81" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="-6" dy="4" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.639215686   0 0 0 0 0.694117647   0 0 0 0 0.749019608  0 0 0 0.3 0" type="matrix" in="shadowOffsetOuter1"></feColorMatrix>
        </filter>
        <path d="M39.654321,130.992298 C39.654321,110.09311 40.5351746,89.1940983 42.2968819,68.2952631 L42.2968835,68.2952632 C43.2666685,56.7908671 52.197293,47.5570778 63.6629191,46.2039222 C78.8424188,44.4124609 94.0195118,43.5167302 109.194198,43.5167302 C124.360772,43.5167302 139.527341,44.4115034 154.693904,46.2010498 L154.693904,46.2010495 C166.164025,47.5544421 175.09671,56.7944422 176.061501,68.3037662 C177.833381,89.4411461 178.719321,110.482698 178.719321,131.428422 C178.719321,152.307984 177.838969,173.187319 176.078265,194.066429 L176.078265,194.066429 C175.10807,205.571375 166.17633,214.805149 154.709997,216.157282 C139.486491,217.95247 124.287302,218.850064 109.112431,218.850064 C93.9666716,218.850064 78.8209665,217.95591 63.6753158,216.167604 L63.6753156,216.167605 C52.2067135,214.813462 43.275345,205.574785 42.309801,194.06695 C40.539481,172.967394 39.654321,151.94251 39.654321,130.992298 Z" id="path-3"></path>
        <filter x="-6.0%" y="-2.3%" width="108.6%" height="105.9%" filterUnits="objectBoundingBox" id="filter-4">
            <feMorphology radius="0.81" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="-5" dy="3" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.639215686   0 0 0 0 0.694117647   0 0 0 0 0.749019608  0 0 0 0.3 0" type="matrix" in="shadowOffsetOuter1"></feColorMatrix>
        </filter>
        <path d="M161.510068,89.8583333 L176.8425,89.8583333 L176.8425,107.391667 L161.510068,107.391667 L161.510068,107.391667 C157.8829,107.391667 154.9425,104.451267 154.9425,100.824099 L154.9425,96.4259009 L154.9425,96.4259009 C154.9425,92.7987335 157.8829,89.8583333 161.510068,89.8583333 Z" id="path-5"></path>
        <filter x="-13.7%" y="-5.7%" width="118.3%" height="117.1%" filterUnits="objectBoundingBox" id="filter-6">
            <feOffset dx="-2" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.639215686   0 0 0 0 0.694117647   0 0 0 0 0.749019608  0 0 0 0.3 0" type="matrix" in="shadowOffsetOuter1"></feColorMatrix>
        </filter>
        <path d="M183.957568,89.8583333 L199.29,89.8583333 L199.29,107.391667 L183.957568,107.391667 L183.957568,107.391667 C180.3304,107.391667 177.39,104.451267 177.39,100.824099 L177.39,96.4259009 L177.39,96.4259009 C177.39,92.7987335 180.3304,89.8583333 183.957568,89.8583333 Z" id="path-7"></path>
        <filter x="-2.3%" y="-2.9%" width="109.1%" height="111.4%" filterUnits="objectBoundingBox" id="filter-8">
            <feOffset dx="1" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.639215686   0 0 0 0 0.694117647   0 0 0 0 0.749019608  0 0 0 0.3 0" type="matrix" in="shadowOffsetOuter1"></feColorMatrix>
        </filter>
        <filter x="-55.5%" y="-9.3%" width="177.5%" height="148.0%" filterUnits="objectBoundingBox" id="filter-9">
            <feOffset dx="-1" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.639215686   0 0 0 0 0.694117647   0 0 0 0 0.749019608  0 0 0 0.3 0" type="matrix" in="shadowOffsetOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <filter x="-55.5%" y="-9.3%" width="177.5%" height="148.0%" filterUnits="objectBoundingBox" id="filter-10">
            <feOffset dx="-1" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.639215686   0 0 0 0 0.694117647   0 0 0 0 0.749019608  0 0 0 0.3 0" type="matrix" in="shadowOffsetOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <path d="M8.86493515,17.2480344 C8.4007802,16.9327578 8.09567896,16.3993146 8.09567896,15.7943295 C8.09567896,14.82567 8.87784133,14.0404164 9.84268751,14.0404164 C10.8075337,14.0404164 11.5896961,14.82567 11.5896961,15.7943295 C11.5896961,16.3993146 11.2845948,16.9327578 10.8204399,17.2480344 L11.7961308,19.984547 C11.9101195,20.3042502 11.743355,20.6558271 11.4236517,20.7698158 C11.3574028,20.7934365 11.2875908,20.8055099 11.2172568,20.8055099 L8.46811816,20.8055099 L8.46811816,20.8055099 C8.1287016,20.8055099 7.85355011,20.5303584 7.85355011,20.1909418 C7.85355011,20.1206079 7.86562345,20.0507959 7.8892442,19.984547 L8.86493515,17.2480344 Z" id="path-11"></path>
        <filter x="-12.0%" y="-7.4%" width="124.1%" height="114.8%" filterUnits="objectBoundingBox" id="filter-12">
            <feOffset dx="-1" dy="1" in="SourceAlpha" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.639215686   0 0 0 0 0.694117647   0 0 0 0 0.749019608  0 0 0 1 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <filter x="-50.0%" y="-10.0%" width="160.0%" height="160.0%" filterUnits="objectBoundingBox" id="filter-13">
            <feOffset dx="-1" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.639215686   0 0 0 0 0.694117647   0 0 0 0 0.749019608  0 0 0 0.3 0" type="matrix" in="shadowOffsetOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <filter x="-50.0%" y="-10.0%" width="160.0%" height="160.0%" filterUnits="objectBoundingBox" id="filter-14">
            <feOffset dx="-1" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.639215686   0 0 0 0 0.694117647   0 0 0 0 0.749019608  0 0 0 0.3 0" type="matrix" in="shadowOffsetOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <filter x="-50.0%" y="-10.0%" width="160.0%" height="160.0%" filterUnits="objectBoundingBox" id="filter-15">
            <feOffset dx="-1" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.639215686   0 0 0 0 0.694117647   0 0 0 0 0.749019608  0 0 0 0.3 0" type="matrix" in="shadowOffsetOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <filter x="-50.0%" y="-10.0%" width="160.0%" height="160.0%" filterUnits="objectBoundingBox" id="filter-16">
            <feOffset dx="-1" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.639215686   0 0 0 0 0.694117647   0 0 0 0 0.749019608  0 0 0 0.3 0" type="matrix" in="shadowOffsetOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <filter x="-50.0%" y="-10.0%" width="160.0%" height="160.0%" filterUnits="objectBoundingBox" id="filter-17">
            <feOffset dx="-1" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.639215686   0 0 0 0 0.694117647   0 0 0 0 0.749019608  0 0 0 0.3 0" type="matrix" in="shadowOffsetOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <filter x="-50.0%" y="-10.0%" width="160.0%" height="160.0%" filterUnits="objectBoundingBox" id="filter-18">
            <feOffset dx="-1" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.639215686   0 0 0 0 0.694117647   0 0 0 0 0.749019608  0 0 0 0.3 0" type="matrix" in="shadowOffsetOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <filter x="-50.0%" y="-10.0%" width="160.0%" height="160.0%" filterUnits="objectBoundingBox" id="filter-19">
            <feOffset dx="-1" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.639215686   0 0 0 0 0.694117647   0 0 0 0 0.749019608  0 0 0 0.3 0" type="matrix" in="shadowOffsetOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <filter x="-50.0%" y="-10.0%" width="160.0%" height="160.0%" filterUnits="objectBoundingBox" id="filter-20">
            <feOffset dx="-1" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.639215686   0 0 0 0 0.694117647   0 0 0 0 0.749019608  0 0 0 0.3 0" type="matrix" in="shadowOffsetOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <filter x="-50.0%" y="-10.0%" width="160.0%" height="160.0%" filterUnits="objectBoundingBox" id="filter-21">
            <feOffset dx="-1" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.639215686   0 0 0 0 0.694117647   0 0 0 0 0.749019608  0 0 0 0.3 0" type="matrix" in="shadowOffsetOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <filter x="-50.0%" y="-10.0%" width="160.0%" height="160.0%" filterUnits="objectBoundingBox" id="filter-22">
            <feOffset dx="-1" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.639215686   0 0 0 0 0.694117647   0 0 0 0 0.749019608  0 0 0 0.3 0" type="matrix" in="shadowOffsetOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <filter x="-44.4%" y="-20.0%" width="166.7%" height="160.0%" filterUnits="objectBoundingBox" id="filter-23">
            <feOffset dx="-2" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.639215686   0 0 0 0 0.694117647   0 0 0 0 0.749019608  0 0 0 0.3 0" type="matrix" in="shadowOffsetOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <filter x="-44.4%" y="-20.0%" width="166.7%" height="160.0%" filterUnits="objectBoundingBox" id="filter-24">
            <feOffset dx="-2" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.639215686   0 0 0 0 0.694117647   0 0 0 0 0.749019608  0 0 0 0.3 0" type="matrix" in="shadowOffsetOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <filter x="-44.4%" y="-20.0%" width="166.7%" height="160.0%" filterUnits="objectBoundingBox" id="filter-25">
            <feOffset dx="-2" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.639215686   0 0 0 0 0.694117647   0 0 0 0 0.749019608  0 0 0 0.3 0" type="matrix" in="shadowOffsetOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <filter x="-44.4%" y="-20.0%" width="166.7%" height="160.0%" filterUnits="objectBoundingBox" id="filter-26">
            <feOffset dx="-2" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.639215686   0 0 0 0 0.694117647   0 0 0 0 0.749019608  0 0 0 0.3 0" type="matrix" in="shadowOffsetOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <filter x="-44.4%" y="-20.0%" width="166.7%" height="160.0%" filterUnits="objectBoundingBox" id="filter-27">
            <feOffset dx="-2" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.639215686   0 0 0 0 0.694117647   0 0 0 0 0.749019608  0 0 0 0.3 0" type="matrix" in="shadowOffsetOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <filter x="-44.4%" y="-20.0%" width="166.7%" height="160.0%" filterUnits="objectBoundingBox" id="filter-28">
            <feOffset dx="-2" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.639215686   0 0 0 0 0.694117647   0 0 0 0 0.749019608  0 0 0 0.3 0" type="matrix" in="shadowOffsetOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <filter x="-44.4%" y="-20.0%" width="166.7%" height="160.0%" filterUnits="objectBoundingBox" id="filter-29">
            <feOffset dx="-2" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.639215686   0 0 0 0 0.694117647   0 0 0 0 0.749019608  0 0 0 0.3 0" type="matrix" in="shadowOffsetOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <rect id="path-30" x="79.0833333" y="68.7939815" width="60.8333333" height="24.3518519"></rect>
        <filter x="-2.5%" y="-2.1%" width="103.3%" height="108.2%" filterUnits="objectBoundingBox" id="filter-31">
            <feOffset dx="-1" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.639215686   0 0 0 0 0.694117647   0 0 0 0 0.749019608  0 0 0 0.3 0" type="matrix" in="shadowOffsetOuter1"></feColorMatrix>
        </filter>
        <rect id="path-32" x="119.233333" y="71.2346165" width="18.2504951" height="19.5072939"></rect>
        <path d="M6.69173423,0.214583333 L15.4541667,0.214583333 L15.4541667,30.8979167 L6.69173423,30.8979167 L6.69173423,30.8979167 C3.06456682,30.8979167 0.124166667,27.9575165 0.124166667,24.3303491 L0.124166667,6.7821509 L0.124166667,6.7821509 C0.124166667,3.15498349 3.06456682,0.214583333 6.69173423,0.214583333 Z" id="path-34"></path>
        <filter x="-29.4%" y="-4.9%" width="139.1%" height="116.3%" filterUnits="objectBoundingBox" id="filter-35">
            <feOffset dx="-3" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.866666667   0 0 0 0 0.890196078   0 0 0 0 0.909803922  0 0 0 1 0" type="matrix" in="shadowOffsetOuter1"></feColorMatrix>
        </filter>
        <filter x="-50.0%" y="-10.0%" width="160.0%" height="160.0%" filterUnits="objectBoundingBox" id="filter-36">
            <feOffset dx="-1" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.639215686   0 0 0 0 0.694117647   0 0 0 0 0.749019608  0 0 0 0.3 0" type="matrix" in="shadowOffsetOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <filter x="-50.0%" y="-10.0%" width="160.0%" height="160.0%" filterUnits="objectBoundingBox" id="filter-37">
            <feOffset dx="-1" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.639215686   0 0 0 0 0.694117647   0 0 0 0 0.749019608  0 0 0 0.3 0" type="matrix" in="shadowOffsetOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <path d="M42.8291667,11.875589 L42.8291667,19.236911 C42.8291667,19.9264853 42.3373793,20.5354959 41.738915,20.595957 L17.6441667,22.6791667 L17.6441667,8.43333333 L41.738915,10.516543 C42.3410444,10.5773757 42.8291667,11.185286 42.8291667,11.875589 Z" id="path-38"></path>
        <filter x="-50.0%" y="-10.0%" width="160.0%" height="160.0%" filterUnits="objectBoundingBox" id="filter-39">
            <feOffset dx="-1" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.639215686   0 0 0 0 0.694117647   0 0 0 0 0.749019608  0 0 0 0.3 0" type="matrix" in="shadowOffsetOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <filter x="-50.0%" y="-10.0%" width="160.0%" height="160.0%" filterUnits="objectBoundingBox" id="filter-40">
            <feOffset dx="-1" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.639215686   0 0 0 0 0.694117647   0 0 0 0 0.749019608  0 0 0 0.3 0" type="matrix" in="shadowOffsetOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <rect id="path-41" x="12.23" y="6.78958333" width="7.1175" height="17.5333333" rx="1.09459459"></rect>
        <filter x="-42.1%" y="-5.7%" width="156.2%" height="117.1%" filterUnits="objectBoundingBox" id="filter-42">
            <feOffset dx="-2" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.639215686   0 0 0 0 0.694117647   0 0 0 0 0.749019608  0 0 0 0.3 0" type="matrix" in="shadowOffsetOuter1"></feColorMatrix>
        </filter>
        <path d="M6.69173423,0.214583333 L15.4541667,0.214583333 L15.4541667,30.8979167 L6.69173423,30.8979167 L6.69173423,30.8979167 C3.06456682,30.8979167 0.124166667,27.9575165 0.124166667,24.3303491 L0.124166667,6.7821509 L0.124166667,6.7821509 C0.124166667,3.15498349 3.06456682,0.214583333 6.69173423,0.214583333 Z" id="path-43"></path>
        <filter x="-29.4%" y="-4.9%" width="139.1%" height="116.3%" filterUnits="objectBoundingBox" id="filter-44">
            <feOffset dx="-3" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.866666667   0 0 0 0 0.890196078   0 0 0 0 0.909803922  0 0 0 1 0" type="matrix" in="shadowOffsetOuter1"></feColorMatrix>
        </filter>
        <filter x="-50.0%" y="-10.0%" width="160.0%" height="160.0%" filterUnits="objectBoundingBox" id="filter-45">
            <feOffset dx="-1" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.639215686   0 0 0 0 0.694117647   0 0 0 0 0.749019608  0 0 0 0.3 0" type="matrix" in="shadowOffsetOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <filter x="-50.0%" y="-10.0%" width="160.0%" height="160.0%" filterUnits="objectBoundingBox" id="filter-46">
            <feOffset dx="-1" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.639215686   0 0 0 0 0.694117647   0 0 0 0 0.749019608  0 0 0 0.3 0" type="matrix" in="shadowOffsetOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <path d="M42.8291667,11.875589 L42.8291667,19.236911 C42.8291667,19.9264853 42.3373793,20.5354959 41.738915,20.595957 L17.6441667,22.6791667 L17.6441667,8.43333333 L41.738915,10.516543 C42.3410444,10.5773757 42.8291667,11.185286 42.8291667,11.875589 Z" id="path-47"></path>
        <filter x="-50.0%" y="-10.0%" width="160.0%" height="160.0%" filterUnits="objectBoundingBox" id="filter-48">
            <feOffset dx="-1" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.639215686   0 0 0 0 0.694117647   0 0 0 0 0.749019608  0 0 0 0.3 0" type="matrix" in="shadowOffsetOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <filter x="-50.0%" y="-10.0%" width="160.0%" height="160.0%" filterUnits="objectBoundingBox" id="filter-49">
            <feOffset dx="-1" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.639215686   0 0 0 0 0.694117647   0 0 0 0 0.749019608  0 0 0 0.3 0" type="matrix" in="shadowOffsetOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <rect id="path-50" x="12.23" y="6.78958333" width="7.1175" height="17.5333333" rx="1.09459459"></rect>
        <filter x="-42.1%" y="-5.7%" width="156.2%" height="117.1%" filterUnits="objectBoundingBox" id="filter-51">
            <feOffset dx="-2" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.639215686   0 0 0 0 0.694117647   0 0 0 0 0.749019608  0 0 0 0.3 0" type="matrix" in="shadowOffsetOuter1"></feColorMatrix>
        </filter>
        <path d="M24.5975819,18.9156325 C22.7107005,18.6906938 20.1873316,18.3702571 18.5998357,18.0903387 C15.7716385,17.5916513 10.2254435,16.2267099 10.2254435,16.2267099 L9.43228672,20.7249253 C9.43228672,20.7249253 15.1331234,21.2766981 17.950402,21.7734603 C19.3152185,22.0141143 21.3273422,22.4758916 23.0359088,22.887195 C22.0718328,24.3735021 20.9147108,26.1229174 20.0932954,27.2534987 C18.405278,29.5768553 14.7471849,33.9633914 14.7471849,33.9633914 L18.4424572,36.6481639 C18.4424572,36.6481639 21.4374695,31.7661671 23.1189701,29.4517801 C23.9598468,28.2944125 25.3148663,26.6203912 26.4570264,25.2388063 C27.4382453,26.7462318 28.6051214,28.5736505 29.3214044,29.8142891 C30.7573178,32.301364 33.2767374,37.4273633 33.2767374,37.4273633 L37.2324017,35.1435594 C37.2324017,35.1435594 33.990657,30.4217925 32.5602872,27.9443193 C31.8079686,26.6412654 30.764866,24.5861738 29.94691,22.9346427 C31.7349883,22.451283 33.9931664,21.8608332 35.4733174,21.5462174 C38.2823878,20.9491311 43.9360463,20.1370415 43.9360463,20.1370415 L42.9863873,15.669247 C42.9863873,15.669247 37.4939659,17.2932232 34.6957402,17.8880045 C33.1431973,18.2180076 30.6947576,18.5971515 28.8277097,18.8686298 C28.722602,16.9801623 28.6032977,14.4864013 28.6032977,12.8962937 C28.6032977,10.024467 28.984416,4.32551158 28.984416,4.32551158 L24.4168083,4.32551158 C24.4168083,4.32551158 24.8633581,10.0355541 24.8633581,12.8962937 C24.8633581,14.5048733 24.7214143,17.0240015 24.5975819,18.9156325 Z" id="path-52"></path>
        <filter x="-14.6%" y="-6.2%" width="120.9%" height="121.4%" filterUnits="objectBoundingBox" id="filter-54">
            <feMorphology radius="0.547297297" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="-3" dy="3" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feComposite in="shadowOffsetOuter1" in2="SourceAlpha" operator="out" result="shadowOffsetOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.639215686   0 0 0 0 0.694117647   0 0 0 0 0.749019608  0 0 0 0.3 0" type="matrix" in="shadowOffsetOuter1"></feColorMatrix>
        </filter>
        <ellipse id="path-55" cx="26.802716" cy="22.6705733" rx="4.38" ry="4.38333333"></ellipse>
        <filter x="-34.2%" y="-11.4%" width="145.7%" height="134.2%" filterUnits="objectBoundingBox" id="filter-56">
            <feOffset dx="-2" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.596078431   0 0 0 0 0.647058824   0 0 0 0 0.701960784  0 0 0 1 0" type="matrix" in="shadowOffsetOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="Ant-Design-Pro-3.0" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="403" transform="translate(-592.000000, -260.000000)">
            <g id="Group-9" transform="translate(599.000000, 261.000000)">
                <g id="Group-11">
                    <g id="Rectangle-355">
                        <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                        <use stroke="#A3B1BF" stroke-width="1.62" fill="#F0F2F5" fill-rule="evenodd" xlink:href="#path-1"></use>
                    </g>
                    <g id="Rectangle-355">
                        <use fill="black" fill-opacity="1" filter="url(#filter-4)" xlink:href="#path-3"></use>
                        <use stroke="#A3B1BF" stroke-width="1.62" fill-opacity="0.15" fill="#A3B1BF" fill-rule="evenodd" xlink:href="#path-3"></use>
                    </g>
                    <g id="Rectangle-357">
                        <use fill="black" fill-opacity="1" filter="url(#filter-6)" xlink:href="#path-5"></use>
                        <use fill="#F0F2F5" fill-rule="evenodd" xlink:href="#path-5"></use>
                        <path stroke="#A3B1BF" stroke-width="1.62" d="M176.0325,106.581667 L176.0325,90.6683333 L161.510068,90.6683333 C158.330251,90.6683333 155.7525,93.2460841 155.7525,96.4259009 L155.7525,100.824099 C155.7525,104.003916 158.330251,106.581667 161.510068,106.581667 L176.0325,106.581667 Z"></path>
                    </g>
                    <g id="Rectangle-357" transform="translate(188.340000, 98.625000) scale(-1, 1) translate(-188.340000, -98.625000) ">
                        <use fill="black" fill-opacity="1" filter="url(#filter-8)" xlink:href="#path-7"></use>
                        <use fill="#F0F2F5" fill-rule="evenodd" xlink:href="#path-7"></use>
                        <path stroke="#A3B1BF" stroke-width="1.62" d="M198.48,106.581667 L198.48,90.6683333 L183.957568,90.6683333 C180.777751,90.6683333 178.2,93.2460841 178.2,96.4259009 L178.2,100.824099 C178.2,104.003916 180.777751,106.581667 183.957568,106.581667 L198.48,106.581667 Z"></path>
                    </g>
                    <path d="M169.537821,109.198898 L179.520726,109.198898 L179.520726,106.316665 C179.657239,106.659699 174.968976,106.316665 172.419859,106.316665 L174.153055,100.243742 C171.605657,100.243742 169.537821,102.294522 169.537821,104.822572 L169.537821,109.198898 Z M181.404585,106.764356 L181.334952,105.82938 C178.493277,106.319657 175.877716,105.82938 172.088276,105.82938 L174.153055,98.0162037 C170.365914,98.0162037 167.291667,101.063519 167.291667,104.822572 L167.291667,106.764356 L166.869647,106.764356 L166.869647,106.764356 C165.758829,106.764356 164.858333,107.664852 164.858333,108.775669 L164.858333,108.775669 L164.858333,115.540394 C164.858333,119.983665 168.460316,123.585648 172.903588,123.585648 L176.279745,123.585648 C180.723017,123.585648 184.325,119.983665 184.325,115.540394 L184.325,108.775669 C184.325,107.664852 183.424504,106.764356 182.313686,106.764356 L181.404585,106.764356 Z" id="Shape" fill="#D4DBE2" fill-rule="nonzero"></path>
                    <g id="Group-6" filter="url(#filter-9)" transform="translate(160.419753, 95.472603)">
                        <ellipse id="Oval-191" stroke="#D9D9D9" stroke-width="1.09459459" fill="#F5F5F5" cx="2.19" cy="2.73958333" rx="2.19" ry="2.19166667"></ellipse>
                        <polygon id="Path-301" fill="#D9D9D9" points="3.78100727 4.39009284 1.17151744 0.497180523 0.223105786 2.08897836 0.101076918 4.39009284"></polygon>
                    </g>
                    <g id="Group-6" filter="url(#filter-10)" transform="translate(189.259259, 95.472603)">
                        <ellipse id="Oval-191" stroke="#D9D9D9" stroke-width="1.09459459" fill="#F5F5F5" cx="2.19" cy="2.73958333" rx="2.19" ry="2.19166667"></ellipse>
                        <polygon id="Path-301" fill="#D9D9D9" points="3.78100727 4.39009284 1.17151744 0.497180523 0.223105786 2.08897836 0.101076918 4.39009284"></polygon>
                    </g>
                    <g id="lock" transform="translate(166.683333, 96.798611)">
                        <path d="M2.43333333,9.34812925 L2.43333333,7.750693 C2.43333333,4.33901358 4.64933408,1.45058816 7.70555556,0.476293029 L7.70555556,4.05203095 C6.57838885,4.84048413 5.84,6.15673686 5.84,7.64734194 L5.84,9.34812925 L14.4830461,9.34812925 C14.4830461,8.8280509 14.4830461,8.25372782 14.4830461,7.64734194 C14.4830461,6.44285214 14.0009139,5.352206 13.2211113,4.56188949 L13.2211113,0.864048014 C15.7633754,2.09038625 17.52,4.7131478 17.52,7.750693 C17.52,8.30041562 17.52,8.83504216 17.52,9.3491485 C18.6008993,9.38327886 19.4666667,10.2702555 19.4666667,11.3594429 L19.4666667,18.3317768 C19.4666667,22.7750482 15.8646835,26.3770314 11.4214121,26.3770314 L8.04525456,26.3770314 C3.60198316,26.3770314 5.4414381e-16,22.7750482 0,18.3317768 L0,11.3594429 C-1.36035952e-16,10.248625 0.900495789,9.34812925 2.01131364,9.34812925 L2.01131364,9.34812925 L2.43333333,9.34812925 Z" id="Combined-Shape" stroke="#A3B1BF" stroke-width="1.62" fill="#F0F2F5"></path>
                        <g id="Oval-1115">
                            <use fill-opacity="0.3" fill="#A3B1BF" fill-rule="evenodd" xlink:href="#path-11"></use>
                            <use fill="black" fill-opacity="1" filter="url(#filter-12)" xlink:href="#path-11"></use>
                        </g>
                    </g>
                    <g id="Group-6" filter="url(#filter-13)" transform="translate(64.000000, 75.000000)">
                        <ellipse id="Oval-191" stroke="#A3B1BF" stroke-width="1.09459459" fill="#F5F5F5" cx="2.19" cy="2.73958333" rx="2.19" ry="2.19166667"></ellipse>
                        <polygon id="Path-301" fill="#A3B1BF" points="3.78100727 4.39009284 1.17151744 0.497180523 0.223105786 2.08897836 0.101076918 4.39009284"></polygon>
                    </g>
                    <g id="Group-6-Copy-11" filter="url(#filter-14)" transform="translate(152.000000, 75.000000)">
                        <ellipse id="Oval-191" stroke="#A3B1BF" stroke-width="1.09459459" fill="#F5F5F5" cx="2.19" cy="2.73958333" rx="2.19" ry="2.19166667"></ellipse>
                        <polygon id="Path-301" fill="#A3B1BF" points="3.78100727 4.39009284 1.17151744 0.497180523 0.223105786 2.08897836 0.101076918 4.39009284"></polygon>
                    </g>
                    <g id="Group-6-Copy-2" filter="url(#filter-15)" transform="translate(57.000000, 120.000000)">
                        <ellipse id="Oval-191" stroke="#A3B1BF" stroke-width="1.09459459" fill="#F5F5F5" cx="2.19" cy="2.73958333" rx="2.19" ry="2.19166667"></ellipse>
                        <polygon id="Path-301" fill="#A3B1BF" points="3.78100727 4.39009284 1.17151744 0.497180523 0.223105786 2.08897836 0.101076918 4.39009284"></polygon>
                    </g>
                    <g id="Group-6-Copy-3" filter="url(#filter-16)" transform="translate(57.000000, 137.000000)">
                        <ellipse id="Oval-191" stroke="#A3B1BF" stroke-width="1.09459459" fill="#F5F5F5" cx="2.19" cy="2.73958333" rx="2.19" ry="2.19166667"></ellipse>
                        <polygon id="Path-301" fill="#A3B1BF" points="3.78100727 4.39009284 1.17151744 0.497180523 0.223105786 2.08897836 0.101076918 4.39009284"></polygon>
                    </g>
                    <g id="Group-6-Copy-4" filter="url(#filter-17)" transform="translate(64.000000, 186.000000)">
                        <ellipse id="Oval-191" stroke="#A3B1BF" stroke-width="1.09459459" fill="#F5F5F5" cx="2.19" cy="2.73958333" rx="2.19" ry="2.19166667"></ellipse>
                        <polygon id="Path-301" fill="#A3B1BF" points="3.78100727 4.39009284 1.17151744 0.497180523 0.223105786 2.08897836 0.101076918 4.39009284"></polygon>
                    </g>
                    <g id="Group-6-Copy-5" filter="url(#filter-18)" transform="translate(93.000000, 191.000000)">
                        <ellipse id="Oval-191" stroke="#A3B1BF" stroke-width="1.09459459" fill="#F5F5F5" cx="2.19" cy="2.73958333" rx="2.19" ry="2.19166667"></ellipse>
                        <polygon id="Path-301" fill="#A3B1BF" points="3.78100727 4.39009284 1.17151744 0.497180523 0.223105786 2.08897836 0.101076918 4.39009284"></polygon>
                    </g>
                    <g id="Group-6-Copy-6" filter="url(#filter-19)" transform="translate(122.000000, 191.000000)">
                        <ellipse id="Oval-191" stroke="#A3B1BF" stroke-width="1.09459459" fill="#F5F5F5" cx="2.19" cy="2.73958333" rx="2.19" ry="2.19166667"></ellipse>
                        <polygon id="Path-301" fill="#A3B1BF" points="3.78100727 4.39009284 1.17151744 0.497180523 0.223105786 2.08897836 0.101076918 4.39009284"></polygon>
                    </g>
                    <g id="Group-6-Copy-7" filter="url(#filter-20)" transform="translate(152.000000, 186.000000)">
                        <ellipse id="Oval-191" stroke="#A3B1BF" stroke-width="1.09459459" fill="#F5F5F5" cx="2.19" cy="2.73958333" rx="2.19" ry="2.19166667"></ellipse>
                        <polygon id="Path-301" fill="#A3B1BF" points="3.78100727 4.39009284 1.17151744 0.497180523 0.223105786 2.08897836 0.101076918 4.39009284"></polygon>
                    </g>
                    <g id="Group-6" filter="url(#filter-21)" transform="translate(160.419753, 95.472603)">
                        <ellipse id="Oval-191" stroke="#A3B1BF" stroke-width="1.09459459" fill="#F5F5F5" cx="2.19" cy="2.73958333" rx="2.19" ry="2.19166667"></ellipse>
                        <polygon id="Path-301" fill="#A3B1BF" points="3.78100727 4.39009284 1.17151744 0.497180523 0.223105786 2.08897836 0.101076918 4.39009284"></polygon>
                    </g>
                    <g id="Group-6" filter="url(#filter-22)" transform="translate(189.259259, 95.472603)">
                        <ellipse id="Oval-191" stroke="#A3B1BF" stroke-width="1.09459459" fill="#F7FAFC" cx="2.19" cy="2.73958333" rx="2.19" ry="2.19166667"></ellipse>
                        <polygon id="Path-301" fill="#A3B1BF" points="3.78100727 4.39009284 1.17151744 0.497180523 0.223105786 2.08897836 0.101076918 4.39009284"></polygon>
                    </g>
                    <g id="Group-6" filter="url(#filter-23)" transform="translate(25.000000, 38.000000)">
                        <ellipse id="Oval-191" stroke="#A3B1BF" stroke-width="1.64189189" fill="#F7FAFC" cx="4.38" cy="4.93125" rx="4.38" ry="4.38333333"></ellipse>
                        <polygon id="Path-301" fill="#A3B1BF" points="4.0041131 9.13310534 7.56201455 8.23226901 2.34303487 0.44644438 0.446211571 3.63004005 0.788921826 7.80592737"></polygon>
                    </g>
                    <g id="Group-6-Copy-8" filter="url(#filter-24)" transform="translate(25.000000, 218.000000)">
                        <ellipse id="Oval-191" stroke="#A3B1BF" stroke-width="1.64189189" fill="#F7FAFC" cx="4.38" cy="4.93125" rx="4.38" ry="4.38333333"></ellipse>
                        <polygon id="Path-301" fill="#A3B1BF" points="4.0041131 9.13310534 7.56201455 8.23226901 2.34303487 0.44644438 0.446211571 3.63004005 0.788921826 7.80592737"></polygon>
                    </g>
                    <g id="Group-6-Copy-9" filter="url(#filter-25)" transform="translate(106.000000, 237.000000)">
                        <ellipse id="Oval-191" stroke="#A3B1BF" stroke-width="1.64189189" fill="#F7FAFC" cx="4.38" cy="4.93125" rx="4.38" ry="4.38333333"></ellipse>
                        <polygon id="Path-301" fill="#A3B1BF" points="4.0041131 9.13310534 7.56201455 8.23226901 2.34303487 0.44644438 0.446211571 3.63004005 0.788921826 7.80592737"></polygon>
                    </g>
                    <g id="Group-6-Copy-10" filter="url(#filter-26)" transform="translate(185.000000, 219.000000)">
                        <ellipse id="Oval-191" stroke="#A3B1BF" stroke-width="1.64189189" fill="#F7FAFC" cx="4.38" cy="4.93125" rx="4.38" ry="4.38333333"></ellipse>
                        <polygon id="Path-301" fill="#A3B1BF" points="4.0041131 9.13310534 7.56201455 8.23226901 2.34303487 0.44644438 0.446211571 3.63004005 0.788921826 7.80592737"></polygon>
                    </g>
                    <g id="Group-6" filter="url(#filter-27)" transform="translate(14.419753, 129.698630)">
                        <ellipse id="Oval-191" stroke="#A3B1BF" stroke-width="1.64189189" fill="#F7FAFC" cx="4.38" cy="4.93125" rx="4.38" ry="4.38333333"></ellipse>
                        <polygon id="Path-301" fill="#A3B1BF" points="4.0041131 9.13310534 7.56201455 8.23226901 2.34303487 0.44644438 0.446211571 3.63004005 0.788921826 7.80592737"></polygon>
                    </g>
                    <g id="Group-6" filter="url(#filter-28)" transform="translate(105.444444, 20.715753)">
                        <ellipse id="Oval-191" stroke="#A3B1BF" stroke-width="1.64189189" fill="#F7FAFC" cx="4.38" cy="4.93125" rx="4.38" ry="4.38333333"></ellipse>
                        <polygon id="Path-301" fill="#A3B1BF" points="4.0041131 9.13310534 7.56201455 8.23226901 2.34303487 0.44644438 0.446211571 3.63004005 0.788921826 7.80592737"></polygon>
                    </g>
                    <g id="Group-6" filter="url(#filter-29)" transform="translate(185.055000, 37.806250)">
                        <ellipse id="Oval-191" stroke="#A3B1BF" stroke-width="1.64189189" fill="#F5F5F5" cx="4.38" cy="4.93125" rx="4.38" ry="4.38333333"></ellipse>
                        <polygon id="Path-301" fill="#A3B1BF" points="4.0041131 9.13310534 7.56201455 8.23226901 2.34303487 0.44644438 0.446211571 3.63004005 0.788921826 7.80592737"></polygon>
                    </g>
                    <g id="Rectangle-373">
                        <use fill="black" fill-opacity="1" filter="url(#filter-31)" xlink:href="#path-30"></use>
                        <use fill="#F0F2F5" fill-rule="evenodd" xlink:href="#path-30"></use>
                        <rect stroke="#A3B1BF" stroke-width="2.43" x="80.2983333" y="70.0089815" width="58.4033333" height="21.9218519"></rect>
                    </g>
                    <mask id="mask-33" fill="white">
                        <use xlink:href="#path-32"></use>
                    </mask>
                    <use id="Mask" fill-opacity="0.3" fill="#A3B1BF" xlink:href="#path-32"></use>
                    <rect id="Rectangle-375" fill="#A3B1BF" x="114.366667" y="77.3171296" width="1.825" height="8.11728395" rx="0.9125"></rect>
                    <rect id="Rectangle-375" fill="#A3B1BF" x="118.016667" y="72.6496914" width="2.43333333" height="16.2345679" rx="1.21666667"></rect>
                    <g id="Group-4" transform="translate(25.000000, 88.000000)">
                        <g id="Rectangle-369">
                            <use fill="black" fill-opacity="1" filter="url(#filter-35)" xlink:href="#path-34"></use>
                            <use fill="#EAEFF3" fill-rule="evenodd" xlink:href="#path-34"></use>
                            <path stroke="#A3B1BF" stroke-width="1.62" d="M14.6441667,30.0879167 L14.6441667,1.02458333 L6.69173423,1.02458333 C3.51191747,1.02458333 0.934166667,3.60233414 0.934166667,6.7821509 L0.934166667,24.3303491 C0.934166667,27.5101659 3.51191747,30.0879167 6.69173423,30.0879167 L14.6441667,30.0879167 Z"></path>
                        </g>
                        <g id="Group-6" filter="url(#filter-36)" transform="translate(6.207500, 6.789583)">
                            <ellipse id="Oval-191" stroke="#A3B1BF" stroke-width="1.09459459" fill="#F7FAFC" cx="2.19" cy="2.73958333" rx="2.19" ry="2.19166667"></ellipse>
                            <polygon id="Path-301" fill="#A3B1BF" points="3.78100727 4.39009284 1.17151744 0.497180523 0.223105786 2.08897836 0.101076918 4.39009284"></polygon>
                        </g>
                        <g id="Group-6" filter="url(#filter-37)" transform="translate(6.543210, 19.181507)">
                            <ellipse id="Oval-191" stroke="#A3B1BF" stroke-width="1.09459459" fill="#F7FAFC" cx="2.19" cy="2.73958333" rx="2.19" ry="2.19166667"></ellipse>
                            <polygon id="Path-301" fill="#A3B1BF" points="3.78100727 4.39009284 1.17151744 0.497180523 0.223105786 2.08897836 0.101076918 4.39009284"></polygon>
                        </g>
                        <g id="Rectangle-371">
                            <use fill="#F0F2F5" fill-rule="evenodd" xlink:href="#path-38"></use>
                            <path stroke="#A3B1BF" stroke-width="1.62" d="M18.4541667,9.31638699 L18.4541667,21.7961161 L41.6574976,19.7900592 C41.8219325,19.7734468 42.0191667,19.5280875 42.0191667,19.236911 L42.0191667,11.875589 C42.0191667,11.5822335 41.824213,11.3392839 41.6691435,11.3235325 L18.4541667,9.31638699 Z"></path>
                        </g>
                        <g id="Group-6" filter="url(#filter-39)" transform="translate(23.000000, 13.000000)">
                            <ellipse id="Oval-191" stroke="#A3B1BF" stroke-width="1.09459459" fill="#F7FAFC" cx="2.19" cy="2.73958333" rx="2.19" ry="2.19166667"></ellipse>
                            <polygon id="Path-301" fill="#A3B1BF" points="3.78100727 4.39009284 1.17151744 0.497180523 0.223105786 2.08897836 0.101076918 4.39009284"></polygon>
                        </g>
                        <g id="Group-6-Copy" filter="url(#filter-40)" transform="translate(33.000000, 13.000000)">
                            <ellipse id="Oval-191" stroke="#A3B1BF" stroke-width="1.09459459" fill="#F7FAFC" cx="2.19" cy="2.73958333" rx="2.19" ry="2.19166667"></ellipse>
                            <polygon id="Path-301" fill="#A3B1BF" points="3.78100727 4.39009284 1.17151744 0.497180523 0.223105786 2.08897836 0.101076918 4.39009284"></polygon>
                        </g>
                        <g id="Rectangle-370">
                            <use fill="black" fill-opacity="1" filter="url(#filter-42)" xlink:href="#path-41"></use>
                            <use fill="#EAEFF3" fill-rule="evenodd" xlink:href="#path-41"></use>
                            <rect stroke="#A3B1BF" stroke-width="1.62" x="13.04" y="7.59958333" width="5.4975" height="15.9133333" rx="1.09459459"></rect>
                        </g>
                        <path d="M13.59875,15.55625 L17.97875,15.55625" id="Line" stroke="#A3B1BF" stroke-width="2.18918919" stroke-linecap="square"></path>
                    </g>
                    <g id="Group-4-Copy" transform="translate(25.000000, 145.000000)">
                        <g id="Rectangle-369">
                            <use fill="black" fill-opacity="1" filter="url(#filter-44)" xlink:href="#path-43"></use>
                            <use fill="#EAEFF3" fill-rule="evenodd" xlink:href="#path-43"></use>
                            <path stroke="#A3B1BF" stroke-width="1.62" d="M14.6441667,30.0879167 L14.6441667,1.02458333 L6.69173423,1.02458333 C3.51191747,1.02458333 0.934166667,3.60233414 0.934166667,6.7821509 L0.934166667,24.3303491 C0.934166667,27.5101659 3.51191747,30.0879167 6.69173423,30.0879167 L14.6441667,30.0879167 Z"></path>
                        </g>
                        <g id="Group-6" filter="url(#filter-45)" transform="translate(6.207500, 6.789583)">
                            <ellipse id="Oval-191" stroke="#A3B1BF" stroke-width="1.09459459" fill="#F7FAFC" cx="2.19" cy="2.73958333" rx="2.19" ry="2.19166667"></ellipse>
                            <polygon id="Path-301" fill="#A3B1BF" points="3.78100727 4.39009284 1.17151744 0.497180523 0.223105786 2.08897836 0.101076918 4.39009284"></polygon>
                        </g>
                        <g id="Group-6" filter="url(#filter-46)" transform="translate(6.543210, 19.181507)">
                            <ellipse id="Oval-191" stroke="#A3B1BF" stroke-width="1.09459459" fill="#F7FAFC" cx="2.19" cy="2.73958333" rx="2.19" ry="2.19166667"></ellipse>
                            <polygon id="Path-301" fill="#A3B1BF" points="3.78100727 4.39009284 1.17151744 0.497180523 0.223105786 2.08897836 0.101076918 4.39009284"></polygon>
                        </g>
                        <g id="Rectangle-371">
                            <use fill="#F0F2F5" fill-rule="evenodd" xlink:href="#path-47"></use>
                            <path stroke="#A3B1BF" stroke-width="1.62" d="M18.4541667,9.31638699 L18.4541667,21.7961161 L41.6574976,19.7900592 C41.8219325,19.7734468 42.0191667,19.5280875 42.0191667,19.236911 L42.0191667,11.875589 C42.0191667,11.5822335 41.824213,11.3392839 41.6691435,11.3235325 L18.4541667,9.31638699 Z"></path>
                        </g>
                        <g id="Group-6" filter="url(#filter-48)" transform="translate(23.000000, 13.000000)">
                            <ellipse id="Oval-191" stroke="#A3B1BF" stroke-width="1.09459459" fill="#F7FAFC" cx="2.19" cy="2.73958333" rx="2.19" ry="2.19166667"></ellipse>
                            <polygon id="Path-301" fill="#A3B1BF" points="3.78100727 4.39009284 1.17151744 0.497180523 0.223105786 2.08897836 0.101076918 4.39009284"></polygon>
                        </g>
                        <g id="Group-6-Copy" filter="url(#filter-49)" transform="translate(33.000000, 13.000000)">
                            <ellipse id="Oval-191" stroke="#A3B1BF" stroke-width="1.09459459" fill="#F7FAFC" cx="2.19" cy="2.73958333" rx="2.19" ry="2.19166667"></ellipse>
                            <polygon id="Path-301" fill="#A3B1BF" points="3.78100727 4.39009284 1.17151744 0.497180523 0.223105786 2.08897836 0.101076918 4.39009284"></polygon>
                        </g>
                        <g id="Rectangle-370">
                            <use fill="black" fill-opacity="1" filter="url(#filter-51)" xlink:href="#path-50"></use>
                            <use fill="#EAEFF3" fill-rule="evenodd" xlink:href="#path-50"></use>
                            <rect stroke="#A3B1BF" stroke-width="1.62" x="13.04" y="7.59958333" width="5.4975" height="15.9133333" rx="1.09459459"></rect>
                        </g>
                        <path d="M13.59875,15.55625 L17.97875,15.55625" id="Line" stroke="#A3B1BF" stroke-width="2.18918919" stroke-linecap="square"></path>
                    </g>
                </g>
                <g id="Group-17" transform="translate(135.185185, 131.500000)">
                    <path d="M22.4835494,46.23099 C10.0861375,46.23099 0.0360493827,36.1732534 0.0360493827,23.7664066 C0.0360493827,11.3595599 10.0861375,1.30182331 22.4835494,1.30182331 C34.8809613,1.30182331 44.9310494,11.3595599 44.9310494,23.7664066 C44.9310494,36.1732534 34.8809613,46.23099 22.4835494,46.23099 Z M22.4835494,39.65599 C31.2524505,39.65599 38.3610494,32.5419812 38.3610494,23.7664066 C38.3610494,14.9908321 31.2524505,7.87682331 22.4835494,7.87682331 C13.7146483,7.87682331 6.60604938,14.9908321 6.60604938,23.7664066 C6.60604938,32.5419812 13.7146483,39.65599 22.4835494,39.65599 Z" id="Oval-190" fill-opacity="0.3" fill="#A3B1BF"></path>
                    <mask id="mask-53" fill="white">
                        <use xlink:href="#path-52"></use>
                    </mask>
                    <g id="Mask">
                        <use fill="black" fill-opacity="1" filter="url(#filter-54)" xlink:href="#path-52"></use>
                        <use stroke="#A3B1BF" stroke-width="1.09459459" fill="#A3B1BF" fill-rule="evenodd" xlink:href="#path-52"></use>
                    </g>
                    <g id="Group-7" transform="translate(0.360494, 0.078598)" fill="#98A5B3">
                        <path d="M20.5636489,0.927088888 C22.1113303,0.589405992 23.7186545,0.411496914 25.3675,0.411496914 C37.7649119,0.411496914 47.815,10.4692335 47.815,22.8760802 C47.815,33.7386653 40.1110508,42.8005273 29.8728116,44.8880725 L27.4696515,38.6276265 C35.2448963,37.5983622 41.245,30.9384541 41.245,22.8760802 C41.245,14.1005057 34.1364011,6.98649691 25.3675,6.98649691 C22.0362626,6.98649691 18.9446369,8.01317718 16.3913275,9.76760576 L15.30877,6.94744709 L20.5636489,0.927088888 Z" id="Oval-190"></path>
                    </g>
                    <path d="M26.802716,45.1351566 C14.4053041,45.1351566 4.35521605,35.0774201 4.35521605,22.6705733 C4.35521605,10.2637265 14.4053041,0.20598998 26.802716,0.20598998 C39.200128,0.20598998 49.250216,10.2637265 49.250216,22.6705733 C49.250216,35.0774201 39.200128,45.1351566 26.802716,45.1351566 Z M26.802716,38.5601566 C35.5716172,38.5601566 42.680216,31.4461479 42.680216,22.6705733 C42.680216,13.8949988 35.5716172,6.78098998 26.802716,6.78098998 C18.0338149,6.78098998 10.925216,13.8949988 10.925216,22.6705733 C10.925216,31.4461479 18.0338149,38.5601566 26.802716,38.5601566 Z" id="Oval-190" fill="#A3B1BF"></path>
                    <path d="M37.5685802,6.47400902 C37.5685802,6.47400902 40.3560433,8.0639828 43.0005786,12.4958138 C45.6451139,16.9276447 45.9870813,21.4863845 45.9870813,21.4863845" id="Line" stroke="#F7FAFC" stroke-width="1.62" stroke-linecap="round"></path>
                    <g id="Oval-199">
                        <use fill="black" fill-opacity="1" filter="url(#filter-56)" xlink:href="#path-55"></use>
                        <use fill="#A3B1BF" fill-rule="evenodd" xlink:href="#path-55"></use>
                    </g>
                    <path d="M27.0403924,24.1861178 C28.3842853,24.1861178 29.4737257,23.0958483 29.4737257,21.7509326 C29.4737257,20.406017 28.3842853,19.3157475 27.0403924,19.3157475 C27.0403924,19.3157475 27.6699621,20.406017 27.6699621,21.7509326 C27.6699621,23.0958483 27.0403924,24.1861178 27.0403924,24.1861178 Z" id="Oval-199" fill="#F7FAFC" transform="translate(28.257059, 21.750933) rotate(-43.000000) translate(-28.257059, -21.750933) "></path>
                </g>
            </g>
        </g>
    </g>
</svg>
