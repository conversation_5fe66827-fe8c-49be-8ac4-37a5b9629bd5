{"name": "kettle-manage-vue", "version": "2.0.0", "private": false, "author": "<EMAIL>", "scripts": {"build": "vue-cli-service build", "test:unit": "vue-cli-service test:unit", "test:e2e": "vue-cli-service test:e2e", "lint": "vue-cli-service lint", "dev": "vue-cli-service serve --open"}, "dependencies": {"@vue/shared": "^3.5.9", "axios": "^0.18.0", "axios-cache-plugin": "^0.1.0", "clipboard": "^2.0.0", "codemirror": "^5.38.0", "copy-to-clipboard": "3.0.8", "countup": "^1.8.2", "cron-parser": "^3.1.0", "cropperjs": "^1.2.2", "dayjs": "^1.7.7", "debounce": "^1.2.0", "echarts": "^3.8.5", "element-plus": "^2.8.3", "element-ui": "^2.13.0", "js-cookie": "^2.2.0", "js-file-download": "^0.4.12", "jszip": "^3.5.0", "lodash": "^4.17.20", "moment": "^2.24.0", "qs": "^6.9.4", "v-click-outside-x": "^4.1.0", "view-design": "^4.3.2", "vue": "^2.6.10", "vue-cookies": "^1.7.4", "vue-cropper": "^0.5.5", "vue-i18n": "^8.22.2", "vue-router": "^3.5.3", "vuedraggable": "^2.16.0", "vuex": "^3.0.1", "wangeditor": "^3.1.1", "xlsx": "^0.13.3", "xlsx-style": "^0.8.13"}, "devDependencies": {"@vue/cli-plugin-babel": "^3.12.1", "@vue/cli-plugin-eslint": "^3.0.1", "@vue/cli-plugin-unit-mocha": "^3.0.1", "@vue/cli-service": "^3.12.1", "@vue/eslint-config-standard": "^3.0.0-beta.10", "@vue/test-utils": "^1.0.0-beta.10", "babel-eslint": "^10.0.3", "babel-plugin-component": "^1.1.1", "babel-plugin-syntax-dynamic-import": "^6.18.0", "chai": "^4.1.2", "eslint-plugin-cypress": "^2.0.1", "less": "^3.10.3", "less-loader": "^4.1.0", "lint-staged": "^6.0.0", "mockjs": "^1.0.1-beta3", "style-resources-loader": "^1.3.2", "vue-cli-plugin-style-resources-loader": "~0.1.4", "vue-template-compiler": "^2.5.21"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.js": ["vue-cli-service lint", "git add"], "*.vue": ["vue-cli-service lint", "git add"]}}