#user  nobody;
worker_processes  1;

events {
  worker_connections  1024;
}


http {
  include       mime.types;
  default_type  application/octet-stream;

  add_header Access-Control-Allow-Origin *;
  add_header Access-Control-Allow-Headers X-Requested-With;
  add_header Access-Control-Allow-Methods GET,POST,OPTIONS;

  client_max_body_size 200m;

  #FastCGI相关参数是为了改善网站的性能：减少资源占用，提高访问速度。下面参数看字面意思都能理解。
  fastcgi_connect_timeout 300;
  fastcgi_send_timeout 300;
  fastcgi_read_timeout 300;
  fastcgi_buffer_size 128k;
  fastcgi_buffers 4 128k;
  fastcgi_busy_buffers_size 128k;
  fastcgi_temp_file_write_size 128k;

  #log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
  #                  '$status $body_bytes_sent "$http_referer" '
  #                  '"$http_user_agent" "$http_x_forwarded_for"';

  #access_log  logs/access.log  main;

  sendfile        on;
  #tcp_nopush     on;

  #keepalive_timeout  0;
  keepalive_timeout  65;

  #gzip  on;

  gzip on;
  gzip_min_length  5k;
  gzip_buffers     4 16k;
  gzip_http_version 1.1;
  gzip_comp_level 3;
  gzip_types       text/plain application/x-javascript text/css application/xml text/javascript application/x-httpd-php image/jpeg image/gif image/png;
  gzip_vary on;

  server {
    listen       80;
    server_name  www.yuenbin.cn;
    add_header Access-Control-Allow-Origin *;

    #charset koi8-r;

    #access_log  logs/host.access.log  main;


    location / {
      try_files $uri $uri/ /kettle-admin/index.html;
      root /opt/apps/htdocs;
      index  index.html index.htm;
    }

    location /kettle-admin {
      root   /opt/apps/htdocs;
      index index.html;
      if (!-e $request_filename){
        rewrite ^/(.*) /kettle-admin/index.html last;
        break;
      }
    }

    location ^~ /kettle-admin/ {
      proxy_pass  http://localhost:9879;
      proxy_redirect default;
      # bForwarded-ForIP
      proxy_set_header  Host  $host:$server_port;
      proxy_set_header  X-Real-IP  $remote_addr;
      proxy_set_header  X-Forwarded-For  $proxy_add_x_forwarded_for;
      proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
    }


    #error_page  404              /404.html;

    # redirect server error pages to the static page /50x.html
    #
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
      root   html;
    }

  }

}
